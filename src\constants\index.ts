// App Constants

export const APP_CONFIG = {
  NAME: 'Budget Tracker India',
  VERSION: '1.0.0',
  DATABASE_NAME: 'budget_tracker.db',
  DATABASE_VERSION: 1,
  API_TIMEOUT: 10000,
  MAX_API_CALLS_PER_MONTH: 25,
  AUTO_LOCK_TIMEOUT: 300000, // 5 minutes
};

export const COLORS = {
  primary: '#2196F3',
  primaryDark: '#1976D2',
  secondary: '#FF9800',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  background: '#F5F5F5',
  surface: '#FFFFFF',
  text: '#212121',
  textSecondary: '#757575',
  border: '#E0E0E0',
  disabled: '#BDBDBD',
};

export const CATEGORY_COLORS = [
  '#F44336', '#E91E63', '#9C27B0', '#673AB7',
  '#3F51B5', '#2196F3', '#03A9F4', '#00BCD4',
  '#009688', '#4CAF50', '#8BC34A', '#CDDC39',
  '#FFEB3B', '#FFC107', '#FF9800', '#FF5722',
];

export const DEFAULT_CATEGORIES = {
  INCOME: [
    { name: 'Salary', icon: 'account-balance-wallet', color: '#4CAF50' },
    { name: 'Business', icon: 'business', color: '#2196F3' },
    { name: 'Investment Returns', icon: 'trending-up', color: '#FF9800' },
    { name: 'Freelance', icon: 'work', color: '#9C27B0' },
    { name: 'Other Income', icon: 'attach-money', color: '#607D8B' },
  ],
  EXPENSE: [
    { name: 'Food & Dining', icon: 'restaurant', color: '#F44336' },
    { name: 'Transportation', icon: 'directions-car', color: '#FF9800' },
    { name: 'Shopping', icon: 'shopping-cart', color: '#E91E63' },
    { name: 'Entertainment', icon: 'movie', color: '#9C27B0' },
    { name: 'Bills & Utilities', icon: 'receipt', color: '#3F51B5' },
    { name: 'Healthcare', icon: 'local-hospital', color: '#4CAF50' },
    { name: 'Education', icon: 'school', color: '#FF5722' },
    { name: 'Travel', icon: 'flight', color: '#00BCD4' },
    { name: 'Groceries', icon: 'local-grocery-store', color: '#8BC34A' },
    { name: 'Fuel', icon: 'local-gas-station', color: '#795548' },
    { name: 'Insurance', icon: 'security', color: '#607D8B' },
    { name: 'EMI/Loans', icon: 'account-balance', color: '#FF5722' },
    { name: 'Other Expenses', icon: 'more-horiz', color: '#9E9E9E' },
  ],
};

export const DEFAULT_PAYMENT_METHODS = [
  { name: 'Cash', type: 'cash', icon: 'money' },
  { name: 'Debit Card', type: 'card', icon: 'credit-card' },
  { name: 'Credit Card', type: 'card', icon: 'credit-card' },
  { name: 'UPI', type: 'upi', icon: 'payment' },
  { name: 'Net Banking', type: 'net_banking', icon: 'account-balance' },
  { name: 'Digital Wallet', type: 'wallet', icon: 'account-balance-wallet' },
];

export const CURRENCY = {
  SYMBOL: '₹',
  CODE: 'INR',
  NAME: 'Indian Rupee',
};

export const DATE_FORMATS = {
  DISPLAY: 'DD MMM YYYY',
  INPUT: 'YYYY-MM-DD',
  DATETIME: 'DD MMM YYYY HH:mm',
  TIME: 'HH:mm',
};

export const RECURRING_FREQUENCIES = [
  { label: 'Daily', value: 'daily' },
  { label: 'Weekly', value: 'weekly' },
  { label: 'Monthly', value: 'monthly' },
  { label: 'Yearly', value: 'yearly' },
];

export const BUDGET_PERIODS = [
  { label: 'Weekly', value: 'weekly' },
  { label: 'Monthly', value: 'monthly' },
  { label: 'Yearly', value: 'yearly' },
];

export const INVESTMENT_TYPES = [
  { label: 'Stocks', value: 'stock', icon: 'trending-up' },
  { label: 'Mutual Funds', value: 'mutual_fund', icon: 'pie-chart' },
  { label: 'Fixed Deposits', value: 'fd', icon: 'account-balance' },
  { label: 'Recurring Deposits', value: 'rd', icon: 'savings' },
];

export const ACCOUNT_TYPES = [
  { label: 'Savings Account', value: 'bank', icon: 'account-balance' },
  { label: 'Credit Card', value: 'credit_card', icon: 'credit-card' },
  { label: 'Cash', value: 'cash', icon: 'money' },
  { label: 'Wallet', value: 'savings', icon: 'account-balance-wallet' },
];

// SMS Parsing Patterns for Indian Banks
export const SMS_PATTERNS = {
  AMOUNT: /(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.[0-9]{2})?)/i,
  DEBIT: /(?:debited|debit|spent|paid|withdrawn)/i,
  CREDIT: /(?:credited|credit|received|deposited)/i,
  ACCOUNT: /(?:a\/c|account|card).*?(\*{0,4}[0-9]{4})/i,
  MERCHANT: /(?:at|to|from)\s+([a-zA-Z0-9\s&.-]+?)(?:\s+on|\s+dated|\s+\*|$)/i,
  DATE: /(?:on|dated)\s+([0-9]{1,2}[-\/][0-9]{1,2}[-\/][0-9]{2,4})/i,
  TIME: /(?:at)\s+([0-9]{1,2}:[0-9]{2})/i,
};

export const BANK_SENDERS = [
  'SBIINB', 'HDFCBK', 'ICICIB', 'AXISBK', 'KOTAKB', 'PNBSMS', 'BOBSMS',
  'CANBNK', 'UNIONB', 'IDBIBK', 'YESBNK', 'INDUSB', 'FEDRAL', 'KARVYB',
  'PAYTM', 'GPAY', 'PHONEPE', 'MOBIKW', 'FREECHARGE', 'AMAZONP',
];

export const API_ENDPOINTS = {
  STOCK_PRICE: 'https://api.example.com/stocks',
  MUTUAL_FUND_NAV: 'https://api.mfapi.in/mf',
  CURRENCY_RATES: 'https://api.exchangerate-api.com/v4/latest/INR',
};

export const NOTIFICATION_TYPES = {
  BUDGET_ALERT: 'budget_alert',
  RECURRING_REMINDER: 'recurring_reminder',
  INVESTMENT_MATURITY: 'investment_maturity',
  GENERAL: 'general',
};

export const STORAGE_KEYS = {
  USER_PIN: 'user_pin',
  BIOMETRIC_ENABLED: 'biometric_enabled',
  APP_SETTINGS: 'app_settings',
  LAST_BACKUP: 'last_backup',
  API_CALL_COUNT: 'api_call_count',
  LAST_API_RESET: 'last_api_reset',
};

export const PERMISSIONS = {
  SMS: 'android.permission.READ_SMS',
  BIOMETRIC: 'android.permission.USE_BIOMETRIC',
  NOTIFICATIONS: 'android.permission.POST_NOTIFICATIONS',
};

export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  INVALID_INPUT: 'INVALID_INPUT',
  API_LIMIT_EXCEEDED: 'API_LIMIT_EXCEEDED',
  SMS_PARSING_ERROR: 'SMS_PARSING_ERROR',
};

export const CHART_CONFIG = {
  backgroundColor: '#ffffff',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  style: {
    borderRadius: 16,
  },
  propsForDots: {
    r: '6',
    strokeWidth: '2',
    stroke: '#2196F3',
  },
};
