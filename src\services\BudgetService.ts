// Budget Service for Database Operations

import { v4 as uuidv4 } from 'react-native-uuid';
import databaseService from '../database';
import { Budget } from '../types';

export class BudgetService {
  async getAllBudgets(): Promise<Budget[]> {
    const query = `
      SELECT b.*, c.name as category_name
      FROM budgets b
      LEFT JOIN categories c ON b.category_id = c.id
      WHERE b.is_active = 1
      ORDER BY b.created_at DESC
    `;
    
    const rows = await databaseService.executeQuery(query);
    return rows.map(this.mapRowToBudget);
  }

  async getBudgetById(id: string): Promise<Budget | null> {
    const query = `
      SELECT b.*, c.name as category_name
      FROM budgets b
      LEFT JOIN categories c ON b.category_id = c.id
      WHERE b.id = ?
    `;
    
    const rows = await databaseService.executeQuery(query, [id]);
    return rows.length > 0 ? this.mapRowToBudget(rows[0]) : null;
  }

  async getActiveBudgets(): Promise<Budget[]> {
    const now = new Date().toISOString();
    const query = `
      SELECT b.*, c.name as category_name
      FROM budgets b
      LEFT JOIN categories c ON b.category_id = c.id
      WHERE b.is_active = 1 
        AND b.start_date <= ? 
        AND b.end_date >= ?
      ORDER BY b.created_at DESC
    `;
    
    const rows = await databaseService.executeQuery(query, [now, now]);
    return rows.map(this.mapRowToBudget);
  }

  async getBudgetsByCategory(categoryId: string): Promise<Budget[]> {
    const query = `
      SELECT b.*, c.name as category_name
      FROM budgets b
      LEFT JOIN categories c ON b.category_id = c.id
      WHERE b.category_id = ? AND b.is_active = 1
      ORDER BY b.created_at DESC
    `;
    
    const rows = await databaseService.executeQuery(query, [categoryId]);
    return rows.map(this.mapRowToBudget);
  }

  async createBudget(budget: Omit<Budget, 'id' | 'spent' | 'createdAt' | 'updatedAt'>): Promise<Budget> {
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const query = `
      INSERT INTO budgets (
        id, name, category_id, amount, period, start_date, end_date, 
        spent, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await databaseService.executeQuery(query, [
      id,
      budget.name,
      budget.categoryId,
      budget.amount,
      budget.period,
      budget.startDate.toISOString(),
      budget.endDate.toISOString(),
      0, // Initial spent amount
      budget.isActive ? 1 : 0,
      now,
      now,
    ]);

    const createdBudget = await this.getBudgetById(id);
    if (!createdBudget) {
      throw new Error('Failed to create budget');
    }

    return createdBudget;
  }

  async updateBudget(id: string, updates: Partial<Budget>): Promise<Budget> {
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (updates.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(updates.name);
    }
    if (updates.categoryId !== undefined) {
      updateFields.push('category_id = ?');
      updateValues.push(updates.categoryId);
    }
    if (updates.amount !== undefined) {
      updateFields.push('amount = ?');
      updateValues.push(updates.amount);
    }
    if (updates.period !== undefined) {
      updateFields.push('period = ?');
      updateValues.push(updates.period);
    }
    if (updates.startDate !== undefined) {
      updateFields.push('start_date = ?');
      updateValues.push(updates.startDate.toISOString());
    }
    if (updates.endDate !== undefined) {
      updateFields.push('end_date = ?');
      updateValues.push(updates.endDate.toISOString());
    }
    if (updates.spent !== undefined) {
      updateFields.push('spent = ?');
      updateValues.push(updates.spent);
    }
    if (updates.isActive !== undefined) {
      updateFields.push('is_active = ?');
      updateValues.push(updates.isActive ? 1 : 0);
    }

    updateFields.push('updated_at = ?');
    updateValues.push(new Date().toISOString());
    updateValues.push(id);

    const query = `UPDATE budgets SET ${updateFields.join(', ')} WHERE id = ?`;
    await databaseService.executeQuery(query, updateValues);

    const updatedBudget = await this.getBudgetById(id);
    if (!updatedBudget) {
      throw new Error('Failed to update budget');
    }

    return updatedBudget;
  }

  async deleteBudget(id: string): Promise<void> {
    const query = 'DELETE FROM budgets WHERE id = ?';
    await databaseService.executeQuery(query, [id]);
  }

  async updateBudgetSpent(budgetId: string): Promise<void> {
    const budget = await this.getBudgetById(budgetId);
    if (!budget) return;

    const query = `
      SELECT COALESCE(SUM(amount), 0) as total_spent
      FROM transactions
      WHERE category_id = ? 
        AND type = 'expense'
        AND date BETWEEN ? AND ?
    `;

    const rows = await databaseService.executeQuery(query, [
      budget.categoryId,
      budget.startDate.toISOString(),
      budget.endDate.toISOString(),
    ]);

    const totalSpent = rows[0].total_spent || 0;
    await this.updateBudget(budgetId, { spent: totalSpent });
  }

  async updateAllBudgetSpent(): Promise<void> {
    const activeBudgets = await this.getActiveBudgets();
    
    for (const budget of activeBudgets) {
      await this.updateBudgetSpent(budget.id);
    }
  }

  async getBudgetProgress(budgetId: string): Promise<{ spent: number; remaining: number; percentage: number }> {
    const budget = await this.getBudgetById(budgetId);
    if (!budget) {
      return { spent: 0, remaining: 0, percentage: 0 };
    }

    await this.updateBudgetSpent(budgetId);
    const updatedBudget = await this.getBudgetById(budgetId);
    
    if (!updatedBudget) {
      return { spent: 0, remaining: 0, percentage: 0 };
    }

    const spent = updatedBudget.spent;
    const remaining = Math.max(0, updatedBudget.amount - spent);
    const percentage = updatedBudget.amount > 0 ? (spent / updatedBudget.amount) * 100 : 0;

    return { spent, remaining, percentage };
  }

  async getBudgetAlerts(): Promise<{ budgetId: string; budgetName: string; percentage: number; isOverBudget: boolean }[]> {
    const activeBudgets = await this.getActiveBudgets();
    const alerts: { budgetId: string; budgetName: string; percentage: number; isOverBudget: boolean }[] = [];

    for (const budget of activeBudgets) {
      const progress = await this.getBudgetProgress(budget.id);
      
      // Alert if over 80% or over budget
      if (progress.percentage >= 80) {
        alerts.push({
          budgetId: budget.id,
          budgetName: budget.name,
          percentage: progress.percentage,
          isOverBudget: progress.percentage > 100,
        });
      }
    }

    return alerts;
  }

  async generateBudgetReport(startDate: Date, endDate: Date): Promise<{
    totalBudgeted: number;
    totalSpent: number;
    budgetUtilization: number;
    budgetDetails: Array<{
      budgetName: string;
      budgeted: number;
      spent: number;
      remaining: number;
      percentage: number;
    }>;
  }> {
    const query = `
      SELECT b.*, c.name as category_name
      FROM budgets b
      LEFT JOIN categories c ON b.category_id = c.id
      WHERE b.start_date <= ? AND b.end_date >= ?
    `;

    const budgets = await databaseService.executeQuery(query, [
      endDate.toISOString(),
      startDate.toISOString(),
    ]);

    let totalBudgeted = 0;
    let totalSpent = 0;
    const budgetDetails: Array<{
      budgetName: string;
      budgeted: number;
      spent: number;
      remaining: number;
      percentage: number;
    }> = [];

    for (const budgetRow of budgets) {
      const budget = this.mapRowToBudget(budgetRow);
      const progress = await this.getBudgetProgress(budget.id);

      totalBudgeted += budget.amount;
      totalSpent += progress.spent;

      budgetDetails.push({
        budgetName: budget.name,
        budgeted: budget.amount,
        spent: progress.spent,
        remaining: progress.remaining,
        percentage: progress.percentage,
      });
    }

    const budgetUtilization = totalBudgeted > 0 ? (totalSpent / totalBudgeted) * 100 : 0;

    return {
      totalBudgeted,
      totalSpent,
      budgetUtilization,
      budgetDetails,
    };
  }

  private mapRowToBudget(row: any): Budget {
    return {
      id: row.id,
      name: row.name,
      categoryId: row.category_id,
      amount: row.amount,
      period: row.period,
      startDate: new Date(row.start_date),
      endDate: new Date(row.end_date),
      spent: row.spent,
      isActive: Boolean(row.is_active),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}

export const budgetService = new BudgetService();
