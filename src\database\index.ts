// Database Service for Budget Tracker

import SQLite from 'react-native-sqlite-storage';
import { APP_CONFIG, DEFAULT_CATEGORIES, DEFAULT_PAYMENT_METHODS } from '../constants';
import { CREATE_TABLES, CREATE_INDEXES, INSERT_DEFAULT_DATA } from './schema';

// Enable debugging in development
SQLite.DEBUG(true);
SQLite.enablePromise(true);

class DatabaseService {
  private db: SQLite.SQLiteDatabase | null = null;

  async initializeDatabase(): Promise<void> {
    try {
      this.db = await SQLite.openDatabase({
        name: APP_CONFIG.DATABASE_NAME,
        location: 'default',
        createFromLocation: '~www/budget_tracker.db',
      });

      console.log('Database opened successfully');
      await this.createTables();
      await this.createIndexes();
      await this.insertDefaultData();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const tables = Object.values(CREATE_TABLES);
    for (const tableSQL of tables) {
      await this.db.executeSql(tableSQL);
    }
  }

  private async createIndexes(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const indexes = Object.values(CREATE_INDEXES);
    for (const indexSQL of indexes) {
      await this.db.executeSql(indexSQL);
    }
  }

  private async insertDefaultData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Insert default account and settings
    const defaultData = Object.values(INSERT_DEFAULT_DATA);
    for (const dataSQL of defaultData) {
      await this.db.executeSql(dataSQL);
    }

    // Insert default categories
    await this.insertDefaultCategories();
    
    // Insert default payment methods
    await this.insertDefaultPaymentMethods();
  }

  private async insertDefaultCategories(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Insert income categories
    for (const category of DEFAULT_CATEGORIES.INCOME) {
      const id = `income_${category.name.toLowerCase().replace(/\s+/g, '_')}`;
      await this.db.executeSql(
        `INSERT OR IGNORE INTO categories (id, name, type, color, icon, is_default) 
         VALUES (?, ?, 'income', ?, ?, 1)`,
        [id, category.name, category.color, category.icon]
      );
    }

    // Insert expense categories
    for (const category of DEFAULT_CATEGORIES.EXPENSE) {
      const id = `expense_${category.name.toLowerCase().replace(/\s+/g, '_')}`;
      await this.db.executeSql(
        `INSERT OR IGNORE INTO categories (id, name, type, color, icon, is_default) 
         VALUES (?, ?, 'expense', ?, ?, 1)`,
        [id, category.name, category.color, category.icon]
      );
    }
  }

  private async insertDefaultPaymentMethods(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    for (const method of DEFAULT_PAYMENT_METHODS) {
      const id = `payment_${method.name.toLowerCase().replace(/\s+/g, '_')}`;
      await this.db.executeSql(
        `INSERT OR IGNORE INTO payment_methods (id, name, type) 
         VALUES (?, ?, ?)`,
        [id, method.name, method.type]
      );
    }
  }

  async executeQuery(sql: string, params: any[] = []): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const [results] = await this.db.executeSql(sql, params);
      const rows: any[] = [];
      
      for (let i = 0; i < results.rows.length; i++) {
        rows.push(results.rows.item(i));
      }
      
      return rows;
    } catch (error) {
      console.error('Query execution failed:', error);
      throw error;
    }
  }

  async executeTransaction(queries: { sql: string; params?: any[] }[]): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      await this.db.transaction(async (tx) => {
        for (const query of queries) {
          await tx.executeSql(query.sql, query.params || []);
        }
      });
    } catch (error) {
      console.error('Transaction execution failed:', error);
      throw error;
    }
  }

  async closeDatabase(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
      console.log('Database closed successfully');
    }
  }

  // Backup and restore methods
  async createBackup(): Promise<string> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      // Get all data from all tables
      const backup = {
        timestamp: new Date().toISOString(),
        version: APP_CONFIG.VERSION,
        data: {
          accounts: await this.executeQuery('SELECT * FROM accounts WHERE id != "default_cash"'),
          categories: await this.executeQuery('SELECT * FROM categories WHERE is_default = 0'),
          payment_methods: await this.executeQuery('SELECT * FROM payment_methods'),
          transactions: await this.executeQuery('SELECT * FROM transactions'),
          recurring_transactions: await this.executeQuery('SELECT * FROM recurring_transactions'),
          budgets: await this.executeQuery('SELECT * FROM budgets'),
          investments: await this.executeQuery('SELECT * FROM investments'),
          app_settings: await this.executeQuery('SELECT * FROM app_settings'),
        },
      };

      return JSON.stringify(backup);
    } catch (error) {
      console.error('Backup creation failed:', error);
      throw error;
    }
  }

  async restoreFromBackup(backupData: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const backup = JSON.parse(backupData);
      
      // Clear existing data (except defaults)
      await this.executeTransaction([
        { sql: 'DELETE FROM transactions' },
        { sql: 'DELETE FROM recurring_transactions' },
        { sql: 'DELETE FROM budgets' },
        { sql: 'DELETE FROM investments' },
        { sql: 'DELETE FROM accounts WHERE id != "default_cash"' },
        { sql: 'DELETE FROM categories WHERE is_default = 0' },
        { sql: 'DELETE FROM payment_methods' },
      ]);

      // Restore data
      const { data } = backup;
      
      // Restore accounts
      for (const account of data.accounts || []) {
        await this.executeQuery(
          `INSERT INTO accounts (id, name, type, balance, currency, is_active, created_at, updated_at) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [account.id, account.name, account.type, account.balance, account.currency, 
           account.is_active, account.created_at, account.updated_at]
        );
      }

      // Restore categories
      for (const category of data.categories || []) {
        await this.executeQuery(
          `INSERT INTO categories (id, name, type, color, icon, is_default, parent_id, created_at, updated_at) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [category.id, category.name, category.type, category.color, category.icon,
           category.is_default, category.parent_id, category.created_at, category.updated_at]
        );
      }

      // Restore other data similarly...
      console.log('Data restored successfully');
    } catch (error) {
      console.error('Restore failed:', error);
      throw error;
    }
  }

  // Health check
  async isHealthy(): Promise<boolean> {
    try {
      if (!this.db) return false;
      await this.executeQuery('SELECT 1');
      return true;
    } catch {
      return false;
    }
  }
}

export const databaseService = new DatabaseService();
export default databaseService;
