// Investments Screen - Manage investments

import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants';

const InvestmentsScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.emptyState}>
          <Ionicons name="trending-up-outline" size={64} color={COLORS.TEXT_SECONDARY} />
          <Text style={styles.emptyStateText}>No investments tracked</Text>
          <Text style={styles.emptyStateSubtext}>Start tracking your investments and portfolio</Text>
          <TouchableOpacity style={styles.addButton} onPress={() => navigation.navigate('AddInvestment')}>
            <Text style={styles.addButtonText}>Add Investment</Text>
          </TouchableOpacity>
        </View>
      </View>
      <TouchableOpacity style={styles.fab} onPress={() => navigation.navigate('AddInvestment')}>
        <Ionicons name="add" size={24} color={COLORS.WHITE} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.BACKGROUND },
  content: { flex: 1, justifyContent: 'center', alignItems: 'center', paddingHorizontal: 20 },
  emptyState: { alignItems: 'center' },
  emptyStateText: { fontSize: 18, fontWeight: '600', color: COLORS.TEXT_PRIMARY, marginTop: 16, marginBottom: 8 },
  emptyStateSubtext: { fontSize: 14, color: COLORS.TEXT_SECONDARY, textAlign: 'center', marginBottom: 24 },
  addButton: { backgroundColor: COLORS.PRIMARY, paddingHorizontal: 24, paddingVertical: 12, borderRadius: 8 },
  addButtonText: { color: COLORS.WHITE, fontSize: 16, fontWeight: '600' },
  fab: { position: 'absolute', bottom: 20, right: 20, width: 56, height: 56, borderRadius: 28, backgroundColor: COLORS.PRIMARY, justifyContent: 'center', alignItems: 'center', elevation: 8 },
});

export default InvestmentsScreen;
