// Zustand Store for Budget Tracker

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  User,
  Account,
  Category,
  PaymentMethod,
  Transaction,
  Budget,
  Investment,
  AppSettings,
  AppError,
  AppState,
} from '../types';

interface AppStore extends AppState {
  // Actions
  setUser: (user: User | null) => void;
  setAuthenticated: (isAuthenticated: boolean) => void;
  setAccounts: (accounts: Account[]) => void;
  addAccount: (account: Account) => void;
  updateAccount: (id: string, updates: Partial<Account>) => void;
  deleteAccount: (id: string) => void;
  
  setCategories: (categories: Category[]) => void;
  addCategory: (category: Category) => void;
  updateCategory: (id: string, updates: Partial<Category>) => void;
  deleteCategory: (id: string) => void;
  
  setPaymentMethods: (methods: PaymentMethod[]) => void;
  addPaymentMethod: (method: PaymentMethod) => void;
  updatePaymentMethod: (id: string, updates: Partial<PaymentMethod>) => void;
  deletePaymentMethod: (id: string) => void;
  
  setTransactions: (transactions: Transaction[]) => void;
  addTransaction: (transaction: Transaction) => void;
  updateTransaction: (id: string, updates: Partial<Transaction>) => void;
  deleteTransaction: (id: string) => void;
  
  setBudgets: (budgets: Budget[]) => void;
  addBudget: (budget: Budget) => void;
  updateBudget: (id: string, updates: Partial<Budget>) => void;
  deleteBudget: (id: string) => void;
  
  setInvestments: (investments: Investment[]) => void;
  addInvestment: (investment: Investment) => void;
  updateInvestment: (id: string, updates: Partial<Investment>) => void;
  deleteInvestment: (id: string) => void;
  
  setSettings: (settings: AppSettings) => void;
  updateSettings: (updates: Partial<AppSettings>) => void;
  
  setLoading: (isLoading: boolean) => void;
  setError: (error: AppError | null) => void;
  
  // Computed values
  getTotalBalance: () => number;
  getMonthlyIncome: (month?: Date) => number;
  getMonthlyExpenses: (month?: Date) => number;
  getTotalInvestmentValue: () => number;
  getBudgetProgress: (budgetId: string) => number;
  
  // Reset store
  reset: () => void;
}

const initialState: AppState = {
  user: null,
  isAuthenticated: false,
  accounts: [],
  categories: [],
  paymentMethods: [],
  transactions: [],
  budgets: [],
  investments: [],
  settings: {
    currency: 'INR',
    dateFormat: 'DD MMM YYYY',
    theme: 'light',
    notifications: true,
    biometricAuth: false,
    autoLock: true,
    autoLockTimeout: 300000,
    smsPermission: false,
    backupEnabled: false,
  },
  isLoading: false,
  error: null,
};

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // User actions
      setUser: (user) => set({ user }),
      setAuthenticated: (isAuthenticated) => set({ isAuthenticated }),

      // Account actions
      setAccounts: (accounts) => set({ accounts }),
      addAccount: (account) =>
        set((state) => ({ accounts: [...state.accounts, account] })),
      updateAccount: (id, updates) =>
        set((state) => ({
          accounts: state.accounts.map((account) =>
            account.id === id ? { ...account, ...updates } : account
          ),
        })),
      deleteAccount: (id) =>
        set((state) => ({
          accounts: state.accounts.filter((account) => account.id !== id),
        })),

      // Category actions
      setCategories: (categories) => set({ categories }),
      addCategory: (category) =>
        set((state) => ({ categories: [...state.categories, category] })),
      updateCategory: (id, updates) =>
        set((state) => ({
          categories: state.categories.map((category) =>
            category.id === id ? { ...category, ...updates } : category
          ),
        })),
      deleteCategory: (id) =>
        set((state) => ({
          categories: state.categories.filter((category) => category.id !== id),
        })),

      // Payment method actions
      setPaymentMethods: (paymentMethods) => set({ paymentMethods }),
      addPaymentMethod: (method) =>
        set((state) => ({ paymentMethods: [...state.paymentMethods, method] })),
      updatePaymentMethod: (id, updates) =>
        set((state) => ({
          paymentMethods: state.paymentMethods.map((method) =>
            method.id === id ? { ...method, ...updates } : method
          ),
        })),
      deletePaymentMethod: (id) =>
        set((state) => ({
          paymentMethods: state.paymentMethods.filter((method) => method.id !== id),
        })),

      // Transaction actions
      setTransactions: (transactions) => set({ transactions }),
      addTransaction: (transaction) =>
        set((state) => ({ transactions: [...state.transactions, transaction] })),
      updateTransaction: (id, updates) =>
        set((state) => ({
          transactions: state.transactions.map((transaction) =>
            transaction.id === id ? { ...transaction, ...updates } : transaction
          ),
        })),
      deleteTransaction: (id) =>
        set((state) => ({
          transactions: state.transactions.filter((transaction) => transaction.id !== id),
        })),

      // Budget actions
      setBudgets: (budgets) => set({ budgets }),
      addBudget: (budget) =>
        set((state) => ({ budgets: [...state.budgets, budget] })),
      updateBudget: (id, updates) =>
        set((state) => ({
          budgets: state.budgets.map((budget) =>
            budget.id === id ? { ...budget, ...updates } : budget
          ),
        })),
      deleteBudget: (id) =>
        set((state) => ({
          budgets: state.budgets.filter((budget) => budget.id !== id),
        })),

      // Investment actions
      setInvestments: (investments) => set({ investments }),
      addInvestment: (investment) =>
        set((state) => ({ investments: [...state.investments, investment] })),
      updateInvestment: (id, updates) =>
        set((state) => ({
          investments: state.investments.map((investment) =>
            investment.id === id ? { ...investment, ...updates } : investment
          ),
        })),
      deleteInvestment: (id) =>
        set((state) => ({
          investments: state.investments.filter((investment) => investment.id !== id),
        })),

      // Settings actions
      setSettings: (settings) => set({ settings }),
      updateSettings: (updates) =>
        set((state) => ({ settings: { ...state.settings, ...updates } })),

      // UI actions
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),

      // Computed values
      getTotalBalance: () => {
        const { accounts } = get();
        return accounts.reduce((total, account) => total + account.balance, 0);
      },

      getMonthlyIncome: (month = new Date()) => {
        const { transactions } = get();
        const startOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
        const endOfMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0);

        return transactions
          .filter(
            (t) =>
              t.type === 'income' &&
              new Date(t.date) >= startOfMonth &&
              new Date(t.date) <= endOfMonth
          )
          .reduce((total, t) => total + t.amount, 0);
      },

      getMonthlyExpenses: (month = new Date()) => {
        const { transactions } = get();
        const startOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
        const endOfMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0);

        return transactions
          .filter(
            (t) =>
              t.type === 'expense' &&
              new Date(t.date) >= startOfMonth &&
              new Date(t.date) <= endOfMonth
          )
          .reduce((total, t) => total + t.amount, 0);
      },

      getTotalInvestmentValue: () => {
        const { investments } = get();
        return investments.reduce((total, investment) => total + investment.currentValue, 0);
      },

      getBudgetProgress: (budgetId: string) => {
        const { budgets } = get();
        const budget = budgets.find((b) => b.id === budgetId);
        if (!budget) return 0;
        return budget.amount > 0 ? (budget.spent / budget.amount) * 100 : 0;
      },

      // Reset store
      reset: () => set(initialState),
    }),
    {
      name: 'budget-tracker-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        settings: state.settings,
      }),
    }
  )
);
