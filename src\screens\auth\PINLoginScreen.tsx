// PIN Login Screen

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../hooks/useAuth';
import { COLORS } from '../../constants';

interface PINLoginScreenProps {
  navigation: any;
}

const PINLoginScreen: React.FC<PINLoginScreenProps> = ({ navigation }) => {
  const [pin, setPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [attempts, setAttempts] = useState(0);
  
  const { 
    authenticate, 
    authenticateWithBiometric, 
    isBiometricAvailable, 
    isBiometricEnabled 
  } = useAuth();

  useEffect(() => {
    // Try biometric authentication on mount if available and enabled
    if (isBiometricAvailable && isBiometricEnabled) {
      handleBiometricAuth();
    }
  }, [isBiometricAvailable, isBiometricEnabled]);

  const handleNumberPress = (number: string) => {
    if (pin.length < 6) {
      const newPin = pin + number;
      setPin(newPin);
      
      // Auto-authenticate when PIN is 4-6 digits
      if (newPin.length >= 4) {
        handlePINAuth(newPin);
      }
    }
  };

  const handleBackspace = () => {
    setPin(pin.slice(0, -1));
  };

  const handlePINAuth = async (pinToVerify: string) => {
    setIsLoading(true);
    try {
      const result = await authenticate(pinToVerify);
      if (result.success) {
        // Authentication successful, navigation will be handled by AppNavigator
        setPin('');
        setAttempts(0);
      } else {
        setAttempts(prev => prev + 1);
        setPin('');
        
        if (attempts >= 4) {
          Alert.alert(
            'Too Many Attempts',
            'Please try again later or use biometric authentication.',
            [{ text: 'OK' }]
          );
        } else {
          Alert.alert('Error', result.error || 'Incorrect PIN');
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Authentication failed');
      setPin('');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBiometricAuth = async () => {
    try {
      const result = await authenticateWithBiometric();
      if (!result.success && result.error !== 'Authentication cancelled') {
        // Biometric failed, user can still use PIN
        console.log('Biometric authentication failed:', result.error);
      }
    } catch (error) {
      console.log('Biometric authentication error:', error);
    }
  };

  const renderPINDots = () => {
    return (
      <View style={styles.pinContainer}>
        {[...Array(6)].map((_, index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              index < pin.length && styles.pinDotFilled,
            ]}
          />
        ))}
      </View>
    );
  };

  const renderNumberPad = () => {
    const numbers = [
      ['1', '2', '3'],
      ['4', '5', '6'],
      ['7', '8', '9'],
      ['biometric', '0', 'backspace'],
    ];

    return (
      <View style={styles.numberPad}>
        {numbers.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.numberRow}>
            {row.map((item, itemIndex) => {
              if (item === 'biometric') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={[
                      styles.numberButton,
                      (!isBiometricAvailable || !isBiometricEnabled) && styles.numberButtonDisabled,
                    ]}
                    onPress={handleBiometricAuth}
                    disabled={!isBiometricAvailable || !isBiometricEnabled}
                  >
                    <Ionicons 
                      name="finger-print" 
                      size={24} 
                      color={
                        isBiometricAvailable && isBiometricEnabled 
                          ? COLORS.PRIMARY 
                          : COLORS.DISABLED
                      } 
                    />
                  </TouchableOpacity>
                );
              }
              
              if (item === 'backspace') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={styles.numberButton}
                    onPress={handleBackspace}
                  >
                    <Text style={styles.backspaceText}>⌫</Text>
                  </TouchableOpacity>
                );
              }

              return (
                <TouchableOpacity
                  key={itemIndex}
                  style={styles.numberButton}
                  onPress={() => handleNumberPress(item)}
                  disabled={isLoading}
                >
                  <Text style={styles.numberText}>{item}</Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Ionicons name="wallet" size={60} color={COLORS.PRIMARY} />
        </View>
        <Text style={styles.title}>Budget Tracker</Text>
        <Text style={styles.subtitle}>Enter your PIN to continue</Text>
      </View>

      <View style={styles.content}>
        {renderPINDots()}
        {renderNumberPad()}
      </View>

      {attempts > 0 && (
        <View style={styles.attemptsContainer}>
          <Text style={styles.attemptsText}>
            {5 - attempts} attempts remaining
          </Text>
        </View>
      )}

      <View style={styles.footer}>
        <TouchableOpacity style={styles.forgotPINButton}>
          <Text style={styles.forgotPINText}>Forgot PIN?</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 40,
  },
  logoContainer: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pinContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 60,
  },
  pinDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: COLORS.BORDER,
    marginHorizontal: 8,
  },
  pinDotFilled: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  numberPad: {
    width: 300,
  },
  numberRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  numberButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.SURFACE,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  numberButtonDisabled: {
    backgroundColor: COLORS.DISABLED,
  },
  numberText: {
    fontSize: 24,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  backspaceText: {
    fontSize: 20,
    color: COLORS.TEXT_PRIMARY,
  },
  attemptsContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  attemptsText: {
    fontSize: 14,
    color: COLORS.ERROR,
    fontWeight: '500',
  },
  footer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  forgotPINButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  forgotPINText: {
    fontSize: 16,
    color: COLORS.PRIMARY,
    fontWeight: '500',
  },
});

export default PINLoginScreen;
