import * as React from 'react';
import { TextProps } from 'react-native';
export type Props = TextProps & {
    children: React.ReactNode;
};
/**
 * @deprecated Deprecated in v5.x - use `<Text variant="bodyMedium" />` instead.
 * Typography component for showing a paragraph.
 *
 * <div class="screenshots">
 *   <img src="screenshots/paragraph.png" />
 * </div>
 *
 * ## Usage
 * ```js
 * import * as React from 'react';
 * import { Paragraph } from 'react-native-paper';
 *
 * const MyComponent = () => (
 *   <Paragraph>Paragraph</Paragraph>
 * );
 *
 * export default MyComponent;
 * ```
 */
declare const Paragraph: (props: Props) => React.JSX.Element;
export default Paragraph;
//# sourceMappingURL=Paragraph.d.ts.map