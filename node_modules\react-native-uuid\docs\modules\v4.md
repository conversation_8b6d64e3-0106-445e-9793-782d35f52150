[react-native-uuid](..) / [Exports](../modules.md) / v4

# Module: v4

## Table of contents

### Functions

- [v4](v4.md#v4)

## Functions

### v4

▸ `Const`**v4**(`options?`: *string* \| V4Options, `buf?`: *number*[], `offset?`: *number*): *string* \| *number*[]

#### Parameters:

Name | Type |
:------ | :------ |
`options?` | *string* \| V4Options |
`buf?` | *number*[] |
`offset?` | *number* |

**Returns:** *string* \| *number*[]

Defined in: [v4.ts:14](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/v4.ts#L14)
