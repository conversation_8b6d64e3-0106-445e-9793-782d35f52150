// Main App Navigator

import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

import { useAuth } from '../hooks/useAuth';
import { COLORS } from '../constants';
import LoadingScreen from '../components/LoadingScreen';

// Auth Screens
import PINSetupScreen from '../screens/auth/PINSetupScreen';
import PINLoginScreen from '../screens/auth/PINLoginScreen';

// Main Screens
import DashboardScreen from '../screens/main/DashboardScreen';
import TransactionsScreen from '../screens/main/TransactionsScreen';
import BudgetsScreen from '../screens/main/BudgetsScreen';
import InvestmentsScreen from '../screens/main/InvestmentsScreen';
import SettingsScreen from '../screens/main/SettingsScreen';

// Transaction Screens
import AddTransactionScreen from '../screens/transactions/AddTransactionScreen';
import EditTransactionScreen from '../screens/transactions/EditTransactionScreen';
import TransactionDetailsScreen from '../screens/transactions/TransactionDetailsScreen';

// Budget Screens
import AddBudgetScreen from '../screens/budgets/AddBudgetScreen';
import EditBudgetScreen from '../screens/budgets/EditBudgetScreen';
import BudgetDetailsScreen from '../screens/budgets/BudgetDetailsScreen';

// Investment Screens
import AddInvestmentScreen from '../screens/investments/AddInvestmentScreen';
import EditInvestmentScreen from '../screens/investments/EditInvestmentScreen';
import InvestmentDetailsScreen from '../screens/investments/InvestmentDetailsScreen';

// Settings Screens
import AccountsScreen from '../screens/settings/AccountsScreen';
import CategoriesScreen from '../screens/settings/CategoriesScreen';
import SecurityScreen from '../screens/settings/SecurityScreen';
import BackupScreen from '../screens/settings/BackupScreen';

import { RootStackParamList, MainTabParamList } from '../types';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Dashboard':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Transactions':
              iconName = focused ? 'list' : 'list-outline';
              break;
            case 'Budgets':
              iconName = focused ? 'pie-chart' : 'pie-chart-outline';
              break;
            case 'Investments':
              iconName = focused ? 'trending-up' : 'trending-up-outline';
              break;
            case 'Settings':
              iconName = focused ? 'settings' : 'settings-outline';
              break;
            default:
              iconName = 'home-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: COLORS.PRIMARY,
        tabBarInactiveTintColor: COLORS.TEXT_SECONDARY,
        tabBarStyle: {
          backgroundColor: COLORS.SURFACE,
          borderTopColor: COLORS.BORDER,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        headerStyle: {
          backgroundColor: COLORS.PRIMARY,
        },
        headerTintColor: COLORS.WHITE,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ title: 'Dashboard' }}
      />
      <Tab.Screen 
        name="Transactions" 
        component={TransactionsScreen}
        options={{ title: 'Transactions' }}
      />
      <Tab.Screen 
        name="Budgets" 
        component={BudgetsScreen}
        options={{ title: 'Budgets' }}
      />
      <Tab.Screen 
        name="Investments" 
        component={InvestmentsScreen}
        options={{ title: 'Investments' }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{ title: 'Settings' }}
      />
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  const { isAuthenticated, hasPIN, isLoading } = useAuth();

  console.log('AppNavigator render - isLoading:', isLoading, 'isAuthenticated:', isAuthenticated, 'hasPIN:', hasPIN);

  if (isLoading) {
    return <LoadingScreen message="Initializing app..." />;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: COLORS.PRIMARY,
          },
          headerTintColor: COLORS.WHITE,
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        {!hasPIN ? (
          // User needs to set up PIN
          <Stack.Screen
            name="PINSetup"
            component={PINSetupScreen}
            options={{ 
              title: 'Setup PIN',
              headerShown: false,
            }}
          />
        ) : !isAuthenticated ? (
          // User needs to authenticate
          <Stack.Screen
            name="PINLogin"
            component={PINLoginScreen}
            options={{ 
              title: 'Enter PIN',
              headerShown: false,
            }}
          />
        ) : (
          // Main app screens
          <>
            <Stack.Screen
              name="Main"
              component={MainTabNavigator}
              options={{ headerShown: false }}
            />
            
            {/* Transaction Screens */}
            <Stack.Screen
              name="AddTransaction"
              component={AddTransactionScreen}
              options={{ title: 'Add Transaction' }}
            />
            <Stack.Screen
              name="EditTransaction"
              component={EditTransactionScreen}
              options={{ title: 'Edit Transaction' }}
            />
            <Stack.Screen
              name="TransactionDetails"
              component={TransactionDetailsScreen}
              options={{ title: 'Transaction Details' }}
            />

            {/* Budget Screens */}
            <Stack.Screen
              name="AddBudget"
              component={AddBudgetScreen}
              options={{ title: 'Add Budget' }}
            />
            <Stack.Screen
              name="EditBudget"
              component={EditBudgetScreen}
              options={{ title: 'Edit Budget' }}
            />
            <Stack.Screen
              name="BudgetDetails"
              component={BudgetDetailsScreen}
              options={{ title: 'Budget Details' }}
            />

            {/* Investment Screens */}
            <Stack.Screen
              name="AddInvestment"
              component={AddInvestmentScreen}
              options={{ title: 'Add Investment' }}
            />
            <Stack.Screen
              name="EditInvestment"
              component={EditInvestmentScreen}
              options={{ title: 'Edit Investment' }}
            />
            <Stack.Screen
              name="InvestmentDetails"
              component={InvestmentDetailsScreen}
              options={{ title: 'Investment Details' }}
            />

            {/* Settings Screens */}
            <Stack.Screen
              name="Accounts"
              component={AccountsScreen}
              options={{ title: 'Accounts' }}
            />
            <Stack.Screen
              name="Categories"
              component={CategoriesScreen}
              options={{ title: 'Categories' }}
            />
            <Stack.Screen
              name="Security"
              component={SecurityScreen}
              options={{ title: 'Security' }}
            />
            <Stack.Screen
              name="Backup"
              component={BackupScreen}
              options={{ title: 'Backup & Restore' }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
