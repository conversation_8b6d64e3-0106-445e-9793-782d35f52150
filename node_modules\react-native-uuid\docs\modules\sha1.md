[react-native-uuid](..) / [Exports](../modules.md) / sha1

# Module: sha1

## Table of contents

### References

- [default](sha1.md#default)

### Functions

- [any\_hmac\_sha1](sha1.md#any_hmac_sha1)
- [any\_sha1](sha1.md#any_sha1)
- [b64\_hmac\_sha1](sha1.md#b64_hmac_sha1)
- [b64\_sha1](sha1.md#b64_sha1)
- [hex\_hmac\_sha1](sha1.md#hex_hmac_sha1)
- [hex\_sha1](sha1.md#hex_sha1)

## References

### default

Renames and exports: [hex\_sha1](sha1.md#hex_sha1)

## Functions

### any\_hmac\_sha1

▸ `Const`**any_hmac_sha1**(`k`: *string*, `d`: *string*, `e`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`k` | *string* |
`d` | *string* |
`e` | *string* |

**Returns:** *string*

Defined in: [sha1.ts:38](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/sha1.ts#L38)

___

### any\_sha1

▸ `Const`**any_sha1**(`s`: *string*, `e`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`s` | *string* |
`e` | *string* |

**Returns:** *string*

Defined in: [sha1.ts:29](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/sha1.ts#L29)

___

### b64\_hmac\_sha1

▸ `Const`**b64_hmac_sha1**(`k`: *string*, `d`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`k` | *string* |
`d` | *string* |

**Returns:** *string*

Defined in: [sha1.ts:35](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/sha1.ts#L35)

___

### b64\_sha1

▸ `Const`**b64_sha1**(`s`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`s` | *string* |

**Returns:** *string*

Defined in: [sha1.ts:27](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/sha1.ts#L27)

___

### hex\_hmac\_sha1

▸ `Const`**hex_hmac_sha1**(`k`: *string*, `d`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`k` | *string* |
`d` | *string* |

**Returns:** *string*

Defined in: [sha1.ts:32](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/sha1.ts#L32)

___

### hex\_sha1

▸ `Const`**hex_sha1**(`s`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`s` | *string* |

**Returns:** *string*

Defined in: [sha1.ts:24](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/sha1.ts#L24)
