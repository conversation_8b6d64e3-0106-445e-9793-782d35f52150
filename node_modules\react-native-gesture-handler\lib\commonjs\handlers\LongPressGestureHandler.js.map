{"version": 3, "sources": ["LongPressGestureHandler.ts"], "names": ["longPressGestureHandlerProps", "longPressHandlerName", "LongPressGestureHandler", "name", "allowedProps", "baseGestureHandlerProps", "config", "shouldCancelWhenOutside"], "mappings": ";;;;;;;AACA;;AACA;;;;AAKO,MAAMA,4BAA4B,GAAG,CAC1C,eAD0C,EAE1C,SAF0C,EAG1C,kBAH0C,CAArC;;AAkCA,MAAMC,oBAAoB,GAAG,yBAA7B;AAEP;AACA;AACA;;;;AAGA;AACA;AACA;AACA;AACO,MAAMC,uBAAuB,GAAG,4BAGrC;AACAC,EAAAA,IAAI,EAAEF,oBADN;AAEAG,EAAAA,YAAY,EAAE,CACZ,GAAGC,6CADS,EAEZ,GAAGL,4BAFS,CAFd;AAMAM,EAAAA,MAAM,EAAE;AACNC,IAAAA,uBAAuB,EAAE;AADnB;AANR,CAHqC,CAAhC", "sourcesContent": ["import { LongPressGestureHandlerEventPayload } from './GestureHandlerEventPayload';\nimport createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\nexport const longPressGestureHandlerProps = [\n  'minDurationMs',\n  'maxDist',\n  'numberOfPointers',\n] as const;\n\nexport interface LongPressGestureConfig {\n  /**\n   * Minimum time, expressed in milliseconds, that a finger must remain pressed on\n   * the corresponding view. The default value is 500.\n   */\n  minDurationMs?: number;\n\n  /**\n   * Maximum distance, expressed in points, that defines how far the finger is\n   * allowed to travel during a long press gesture. If the finger travels\n   * further than the defined distance and the handler hasn't yet activated, it\n   * will fail to recognize the gesture. The default value is 10.\n   */\n  maxDist?: number;\n\n  /**\n   * Determine exact number of points required to handle the long press gesture.\n   */\n  numberOfPointers?: number;\n}\n\n/**\n * @deprecated LongPressGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.LongPress()` instead.\n */\nexport interface LongPressGestureHandlerProps\n  extends BaseGestureHandlerProps<LongPressGestureHandlerEventPayload>,\n    LongPressGestureConfig {}\n\nexport const longPressHandlerName = 'LongPressGestureHandler';\n\n/**\n * @deprecated LongPressGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.LongPress()` instead.\n */\nexport type LongPressGestureHandler = typeof LongPressGestureHandler;\n\n/**\n * @deprecated LongPressGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.LongPress()` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const LongPressGestureHandler = createHandler<\n  LongPressGestureHandlerProps,\n  LongPressGestureHandlerEventPayload\n>({\n  name: longPressHandlerName,\n  allowedProps: [\n    ...baseGestureHandlerProps,\n    ...longPressGestureHandlerProps,\n  ] as const,\n  config: {\n    shouldCancelWhenOutside: true,\n  },\n});\n"]}