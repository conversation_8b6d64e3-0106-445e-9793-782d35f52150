// Investment Service - Handle investment operations

import { Investment } from '../types';
import databaseService from '../database';

class InvestmentService {
  async getAllInvestments(): Promise<Investment[]> {
    try {
      const db = await databaseService.getDatabase();
      return new Promise((resolve, reject) => {
        db.transaction((tx) => {
          tx.executeSql(
            'SELECT * FROM investments ORDER BY createdAt DESC',
            [],
            (_, { rows }) => {
              const investments: Investment[] = [];
              for (let i = 0; i < rows.length; i++) {
                const row = rows.item(i);
                investments.push({
                  id: row.id,
                  name: row.name,
                  type: row.type,
                  symbol: row.symbol,
                  quantity: row.quantity,
                  purchasePrice: row.purchasePrice,
                  currentPrice: row.currentPrice,
                  purchaseDate: row.purchaseDate,
                  maturityDate: row.maturityDate,
                  interestRate: row.interestRate,
                  createdAt: row.createdAt,
                  updatedAt: row.updatedAt,
                });
              }
              resolve(investments);
            },
            (_, error) => {
              reject(error);
              return false;
            }
          );
        });
      });
    } catch (error) {
      console.error('Error getting investments:', error);
      throw error;
    }
  }

  async createInvestment(investment: Omit<Investment, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const db = await databaseService.getDatabase();
      const id = Date.now().toString();
      const now = new Date().toISOString();

      return new Promise((resolve, reject) => {
        db.transaction((tx) => {
          tx.executeSql(
            `INSERT INTO investments (
              id, name, type, symbol, quantity, purchasePrice, currentPrice,
              purchaseDate, maturityDate, interestRate, createdAt, updatedAt
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              id,
              investment.name,
              investment.type,
              investment.symbol || null,
              investment.quantity,
              investment.purchasePrice,
              investment.currentPrice,
              investment.purchaseDate,
              investment.maturityDate || null,
              investment.interestRate || null,
              now,
              now,
            ],
            () => {
              resolve(id);
            },
            (_, error) => {
              reject(error);
              return false;
            }
          );
        });
      });
    } catch (error) {
      console.error('Error creating investment:', error);
      throw error;
    }
  }

  async updateInvestment(id: string, updates: Partial<Investment>): Promise<void> {
    try {
      const db = await databaseService.getDatabase();
      const now = new Date().toISOString();

      return new Promise((resolve, reject) => {
        db.transaction((tx) => {
          const setClause = Object.keys(updates)
            .filter(key => key !== 'id' && key !== 'createdAt')
            .map(key => `${key} = ?`)
            .join(', ');

          const values = Object.keys(updates)
            .filter(key => key !== 'id' && key !== 'createdAt')
            .map(key => updates[key as keyof Investment]);

          tx.executeSql(
            `UPDATE investments SET ${setClause}, updatedAt = ? WHERE id = ?`,
            [...values, now, id],
            () => {
              resolve();
            },
            (_, error) => {
              reject(error);
              return false;
            }
          );
        });
      });
    } catch (error) {
      console.error('Error updating investment:', error);
      throw error;
    }
  }

  async deleteInvestment(id: string): Promise<void> {
    try {
      const db = await databaseService.getDatabase();

      return new Promise((resolve, reject) => {
        db.transaction((tx) => {
          tx.executeSql(
            'DELETE FROM investments WHERE id = ?',
            [id],
            () => {
              resolve();
            },
            (_, error) => {
              reject(error);
              return false;
            }
          );
        });
      });
    } catch (error) {
      console.error('Error deleting investment:', error);
      throw error;
    }
  }

  async updateInvestmentPrice(id: string, currentPrice: number): Promise<void> {
    try {
      await this.updateInvestment(id, { currentPrice });
    } catch (error) {
      console.error('Error updating investment price:', error);
      throw error;
    }
  }

  async getInvestmentsByType(type: string): Promise<Investment[]> {
    try {
      const db = await databaseService.getDatabase();
      return new Promise((resolve, reject) => {
        db.transaction((tx) => {
          tx.executeSql(
            'SELECT * FROM investments WHERE type = ? ORDER BY createdAt DESC',
            [type],
            (_, { rows }) => {
              const investments: Investment[] = [];
              for (let i = 0; i < rows.length; i++) {
                const row = rows.item(i);
                investments.push({
                  id: row.id,
                  name: row.name,
                  type: row.type,
                  symbol: row.symbol,
                  quantity: row.quantity,
                  purchasePrice: row.purchasePrice,
                  currentPrice: row.currentPrice,
                  purchaseDate: row.purchaseDate,
                  maturityDate: row.maturityDate,
                  interestRate: row.interestRate,
                  createdAt: row.createdAt,
                  updatedAt: row.updatedAt,
                });
              }
              resolve(investments);
            },
            (_, error) => {
              reject(error);
              return false;
            }
          );
        });
      });
    } catch (error) {
      console.error('Error getting investments by type:', error);
      throw error;
    }
  }

  async clearAllInvestments(): Promise<void> {
    try {
      const db = await databaseService.getDatabase();

      return new Promise((resolve, reject) => {
        db.transaction((tx) => {
          tx.executeSql(
            'DELETE FROM investments',
            [],
            () => {
              resolve();
            },
            (_, error) => {
              reject(error);
              return false;
            }
          );
        });
      });
    } catch (error) {
      console.error('Error clearing investments:', error);
      throw error;
    }
  }

  // Calculate total portfolio value
  calculatePortfolioValue(investments: Investment[]): number {
    return investments.reduce((total, investment) => {
      return total + (investment.quantity * investment.currentPrice);
    }, 0);
  }

  // Calculate total investment cost
  calculateInvestmentCost(investments: Investment[]): number {
    return investments.reduce((total, investment) => {
      return total + (investment.quantity * investment.purchasePrice);
    }, 0);
  }

  // Calculate profit/loss
  calculateProfitLoss(investments: Investment[]): number {
    const currentValue = this.calculatePortfolioValue(investments);
    const investmentCost = this.calculateInvestmentCost(investments);
    return currentValue - investmentCost;
  }

  // Calculate profit/loss percentage
  calculateProfitLossPercentage(investments: Investment[]): number {
    const investmentCost = this.calculateInvestmentCost(investments);
    if (investmentCost === 0) return 0;
    
    const profitLoss = this.calculateProfitLoss(investments);
    return (profitLoss / investmentCost) * 100;
  }
}

export const investmentService = new InvestmentService();
export default investmentService;
