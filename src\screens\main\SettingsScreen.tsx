// Settings Screen - App settings and configuration

import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants';

const SettingsScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const settingsItems = [
    { title: 'Accounts', icon: 'wallet-outline', screen: 'Accounts' },
    { title: 'Categories', icon: 'list-outline', screen: 'Categories' },
    { title: 'Security', icon: 'shield-outline', screen: 'Security' },
    { title: 'Backup & Restore', icon: 'cloud-outline', screen: 'Backup' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>General</Text>
          {settingsItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.settingItem}
              onPress={() => navigation.navigate(item.screen)}
            >
              <View style={styles.settingItemLeft}>
                <Ionicons name={item.icon as any} size={24} color={COLORS.TEXT_PRIMARY} />
                <Text style={styles.settingItemText}>{item.title}</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={COLORS.TEXT_SECONDARY} />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.BACKGROUND },
  scrollView: { flex: 1 },
  section: { marginTop: 20, paddingHorizontal: 16 },
  sectionTitle: { fontSize: 16, fontWeight: '600', color: COLORS.TEXT_SECONDARY, marginBottom: 12, marginLeft: 4 },
  settingItem: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', backgroundColor: COLORS.SURFACE, paddingHorizontal: 16, paddingVertical: 16, marginBottom: 1, borderRadius: 8 },
  settingItemLeft: { flexDirection: 'row', alignItems: 'center' },
  settingItemText: { fontSize: 16, color: COLORS.TEXT_PRIMARY, marginLeft: 12 },
});

export default SettingsScreen;
