[react-native-uuid](..) / [Exports](../modules.md) / md5

# Module: md5

## Table of contents

### References

- [default](md5.md#default)

### Functions

- [any\_hmac\_md5](md5.md#any_hmac_md5)
- [any\_md5](md5.md#any_md5)
- [b64\_hmac\_md5](md5.md#b64_hmac_md5)
- [b64\_md5](md5.md#b64_md5)
- [hex\_hmac\_md5](md5.md#hex_hmac_md5)
- [hex\_md5](md5.md#hex_md5)

## References

### default

Renames and exports: [hex\_md5](md5.md#hex_md5)

## Functions

### any\_hmac\_md5

▸ `Const`**any_hmac_md5**(`k`: *string*, `d`: *string*, `e`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`k` | *string* |
`d` | *string* |
`e` | *string* |

**Returns:** *string*

Defined in: [md5.ts:39](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/md5.ts#L39)

___

### any\_md5

▸ `Const`**any_md5**(`s`: *string*, `e`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`s` | *string* |
`e` | *string* |

**Returns:** *string*

Defined in: [md5.ts:30](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/md5.ts#L30)

___

### b64\_hmac\_md5

▸ `Const`**b64_hmac_md5**(`k`: *string*, `d`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`k` | *string* |
`d` | *string* |

**Returns:** *string*

Defined in: [md5.ts:36](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/md5.ts#L36)

___

### b64\_md5

▸ `Const`**b64_md5**(`s`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`s` | *string* |

**Returns:** *string*

Defined in: [md5.ts:28](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/md5.ts#L28)

___

### hex\_hmac\_md5

▸ `Const`**hex_hmac_md5**(`k`: *string*, `d`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`k` | *string* |
`d` | *string* |

**Returns:** *string*

Defined in: [md5.ts:33](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/md5.ts#L33)

___

### hex\_md5

▸ `Const`**hex_md5**(`s`: *string*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`s` | *string* |

**Returns:** *string*

Defined in: [md5.ts:25](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/md5.ts#L25)
