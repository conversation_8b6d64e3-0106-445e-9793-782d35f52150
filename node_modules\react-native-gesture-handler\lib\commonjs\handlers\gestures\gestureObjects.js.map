{"version": 3, "sources": ["gestureObjects.ts"], "names": ["GestureObjects", "Tap", "TapGesture", "Pan", "PanGesture", "Pinch", "PinchGesture", "Rotation", "RotationGesture", "Fling", "FlingGesture", "Long<PERSON>ress", "LongPressGesture", "ForceTouch", "ForceTouchGesture", "Native", "NativeGesture", "Manual", "ManualGesture", "Hover", "HoverGesture", "Race", "gestures", "ComposedGesture", "Simultaneous", "SimultaneousGesture", "Exclusive", "ExclusiveGesture"], "mappings": ";;;;;;;AAAA;;AACA;;AAEA;;AAKA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMA,cAAc,GAAG;AAC5B;AACF;AACA;AACA;AACEC,EAAAA,GAAG,EAAE,MAAM;AACT,WAAO,IAAIC,sBAAJ,EAAP;AACD,GAP2B;;AAS5B;AACF;AACA;AACA;AACEC,EAAAA,GAAG,EAAE,MAAM;AACT,WAAO,IAAIC,sBAAJ,EAAP;AACD,GAf2B;;AAiB5B;AACF;AACA;AACA;AACEC,EAAAA,KAAK,EAAE,MAAM;AACX,WAAO,IAAIC,0BAAJ,EAAP;AACD,GAvB2B;;AAyB5B;AACF;AACA;AACA;AACEC,EAAAA,QAAQ,EAAE,MAAM;AACd,WAAO,IAAIC,gCAAJ,EAAP;AACD,GA/B2B;;AAiC5B;AACF;AACA;AACA;AACEC,EAAAA,KAAK,EAAE,MAAM;AACX,WAAO,IAAIC,0BAAJ,EAAP;AACD,GAvC2B;;AAyC5B;AACF;AACA;AACA;AACEC,EAAAA,SAAS,EAAE,MAAM;AACf,WAAO,IAAIC,kCAAJ,EAAP;AACD,GA/C2B;;AAiD5B;AACF;AACA;AACA;AACA;AACEC,EAAAA,UAAU,EAAE,MAAM;AAChB,WAAO,IAAIC,oCAAJ,EAAP;AACD,GAxD2B;;AA0D5B;AACF;AACA;AACA;AACA;AACEC,EAAAA,MAAM,EAAE,MAAM;AACZ,WAAO,IAAIC,4BAAJ,EAAP;AACD,GAjE2B;;AAmE5B;AACF;AACA;AACA;AACA;AACA;AACEC,EAAAA,MAAM,EAAE,MAAM;AACZ,WAAO,IAAIC,4BAAJ,EAAP;AACD,GA3E2B;;AA6E5B;AACF;AACA;AACA;AACA;AACA;AACEC,EAAAA,KAAK,EAAE,MAAM;AACX,WAAO,IAAIC,0BAAJ,EAAP;AACD,GArF2B;;AAuF5B;AACF;AACA;AACA;AACA;AACEC,EAAAA,IAAI,EAAE,CAAC,GAAGC,QAAJ,KAA4B;AAChC,WAAO,IAAIC,mCAAJ,CAAoB,GAAGD,QAAvB,CAAP;AACD,GA9F2B;;AAgG5B;AACF;AACA;AACA;AACEE,EAAAA,YAAY,CAAC,GAAGF,QAAJ,EAAyB;AACnC,WAAO,IAAIG,uCAAJ,CAAwB,GAAGH,QAA3B,CAAP;AACD,GAtG2B;;AAwG5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACEI,EAAAA,SAAS,CAAC,GAAGJ,QAAJ,EAAyB;AAChC,WAAO,IAAIK,oCAAJ,CAAqB,GAAGL,QAAxB,CAAP;AACD;;AAlH2B,CAAvB", "sourcesContent": ["import { FlingGesture } from './flingGesture';\nimport { ForceTouchGesture } from './forceTouchGesture';\nimport { Gesture } from './gesture';\nimport {\n  ComposedGesture,\n  ExclusiveGesture,\n  SimultaneousGesture,\n} from './gestureComposition';\nimport { LongPressGesture } from './longPressGesture';\nimport { PanGesture } from './panGesture';\nimport { PinchGesture } from './pinchGesture';\nimport { RotationGesture } from './rotationGesture';\nimport { TapGesture } from './tapGesture';\nimport { NativeGesture } from './nativeGesture';\nimport { ManualGesture } from './manualGesture';\nimport { HoverGesture } from './hoverGesture';\n\n/**\n * `Gesture` is the object that allows you to create and compose gestures.\n *\n * ### Remarks\n * - Consider wrapping your gesture configurations with `useMemo`, as it will reduce the amount of work Gesture Handler has to do under the hood when updating gestures.\n *\n * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/gesture\n */\nexport const GestureObjects = {\n  /**\n   * A discrete gesture that recognizes one or many taps.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/tap-gesture\n   */\n  Tap: () => {\n    return new TapGesture();\n  },\n\n  /**\n   * A continuous gesture that can recognize a panning (dragging) gesture and track its movement.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture\n   */\n  Pan: () => {\n    return new PanGesture();\n  },\n\n  /**\n   * A continuous gesture that recognizes pinch gesture. It allows for tracking the distance between two fingers and use that information to scale or zoom your content.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pinch-gesture\n   */\n  Pinch: () => {\n    return new PinchGesture();\n  },\n\n  /**\n   * A continuous gesture that can recognize rotation and track its movement.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/rotation-gesture\n   */\n  Rotation: () => {\n    return new RotationGesture();\n  },\n\n  /**\n   * A discrete gesture that activates when the movement is sufficiently fast.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/fling-gesture\n   */\n  Fling: () => {\n    return new FlingGesture();\n  },\n\n  /**\n   * A discrete gesture that activates when the corresponding view is pressed for a sufficiently long time.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/long-press-gesture\n   */\n  LongPress: () => {\n    return new LongPressGesture();\n  },\n\n  /**\n   * #### iOS only\n   * A continuous gesture that recognizes force of a touch. It allows for tracking pressure of touch on some iOS devices.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/force-touch-gesture\n   */\n  ForceTouch: () => {\n    return new ForceTouchGesture();\n  },\n\n  /**\n   * A gesture that allows other touch handling components to participate in RNGH's gesture system.\n   * When used, the other component should be the direct child of a `GestureDetector`.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/native-gesture\n   */\n  Native: () => {\n    return new NativeGesture();\n  },\n\n  /**\n   * A plain gesture that has no specific activation criteria nor event data set.\n   * Its state has to be controlled manually using a state manager.\n   * It will not fail when all the pointers are lifted from the screen.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/manual-gesture\n   */\n  Manual: () => {\n    return new ManualGesture();\n  },\n\n  /**\n   * A continuous gesture that can recognize hovering above the view it's attached to.\n   * The hover effect may be activated by moving a mouse or a stylus over the view.\n   *\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/hover-gesture\n   */\n  Hover: () => {\n    return new HoverGesture();\n  },\n\n  /**\n   * Builds a composed gesture consisting of gestures provided as parameters.\n   * The first one that becomes active cancels the rest of gestures.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#race\n   */\n  Race: (...gestures: Gesture[]) => {\n    return new ComposedGesture(...gestures);\n  },\n\n  /**\n   * Builds a composed gesture that allows all base gestures to run simultaneously.\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#simultaneous\n   */\n  Simultaneous(...gestures: Gesture[]) {\n    return new SimultaneousGesture(...gestures);\n  },\n\n  /**\n   * Builds a composed gesture where only one of the provided gestures can become active.\n   * Priority is decided through the order of gestures: the first one has higher priority\n   * than the second one, second one has higher priority than the third one, and so on.\n   * For example, to make a gesture that recognizes both single and double tap you need\n   * to call Exclusive(doubleTap, singleTap).\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#exclusive\n   */\n  Exclusive(...gestures: Gesture[]) {\n    return new ExclusiveGesture(...gestures);\n  },\n};\n"]}