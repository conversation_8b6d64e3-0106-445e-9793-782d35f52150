{"version": 3, "sources": ["longPressGesture.ts"], "names": ["LongPressGesture", "BaseGesture", "constructor", "handler<PERSON>ame", "shouldCancelWhenOutside", "minDuration", "duration", "config", "minDurationMs", "maxDistance", "distance", "maxDist", "numberOfPointers", "pointers"], "mappings": ";;;;;;;AAAA;;;;AAIO,MAAMA,gBAAN,SAA+BC,oBAA/B,CAAgF;AAGrFC,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAF8C,EAE9C;;AAGZ,SAAKC,WAAL,GAAmB,yBAAnB;AACA,SAAKC,uBAAL,CAA6B,IAA7B;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,WAAW,CAACC,QAAD,EAAmB;AAC5B,SAAKC,MAAL,CAAYC,aAAZ,GAA4BF,QAA5B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEG,EAAAA,WAAW,CAACC,QAAD,EAAmB;AAC5B,SAAKH,MAAL,CAAYI,OAAZ,GAAsBD,QAAtB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEE,EAAAA,gBAAgB,CAACC,QAAD,EAAmB;AACjC,SAAKN,MAAL,CAAYK,gBAAZ,GAA+BC,QAA/B;AACA,WAAO,IAAP;AACD;;AArCoF", "sourcesContent": ["import { BaseGesture, BaseGestureConfig } from './gesture';\nimport { LongPressGestureConfig } from '../LongPressGestureHandler';\nimport type { LongPressGestureHandlerEventPayload } from '../GestureHandlerEventPayload';\n\nexport class LongPressGesture extends BaseGesture<LongPressGestureHandlerEventPayload> {\n  public config: BaseGestureConfig & LongPressGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'LongPressGestureHandler';\n    this.shouldCancelWhenOutside(true);\n  }\n\n  /**\n   * Minimum time, expressed in milliseconds, that a finger must remain pressed on the corresponding view.\n   * The default value is 500.\n   * @param duration\n   */\n  minDuration(duration: number) {\n    this.config.minDurationMs = duration;\n    return this;\n  }\n\n  /**\n   * Maximum distance, expressed in points, that defines how far the finger is allowed to travel during a long press gesture.\n   * @param distance\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/long-press-gesture#maxdistancevalue-number\n   */\n  maxDistance(distance: number) {\n    this.config.maxDist = distance;\n    return this;\n  }\n\n  /**\n   * Determine exact number of points required to handle the long press gesture.\n   * @param pointers\n   */\n  numberOfPointers(pointers: number) {\n    this.config.numberOfPointers = pointers;\n    return this;\n  }\n}\n\nexport type LongPressGestureType = InstanceType<typeof LongPressGesture>;\n"]}