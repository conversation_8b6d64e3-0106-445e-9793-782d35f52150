{"version": 3, "names": ["React", "createNavigatorFactory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "MaterialBottomTabView", "MaterialBottomTabNavigator", "id", "initialRouteName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "screenListeners", "screenOptions", "rest", "state", "descriptors", "navigation", "NavigationContent", "createElement", "_extends"], "sourceRoot": "../../../../src", "sources": ["react-navigation/navigators/createMaterialBottomTabNavigator.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SACEC,sBAAsB,EAKtBC,SAAS,EAETC,oBAAoB,QACf,0BAA0B;AAOjC,OAAOC,qBAAqB,MAAM,gCAAgC;AAWlE,SAASC,0BAA0BA,CAAC;EAClCC,EAAE;EACFC,gBAAgB;EAChBC,YAAY;EACZC,QAAQ;EACRC,eAAe;EACfC,aAAa;EACb,GAAGC;AAC4B,CAAC,EAAE;EAClC,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACzDb,oBAAoB,CAMlBD,SAAS,EAAE;IACXI,EAAE;IACFC,gBAAgB;IAChBC,YAAY;IACZC,QAAQ;IACRC,eAAe;IACfC;EACF,CAAC,CAAC;EAEJ,oBACEX,KAAA,CAAAiB,aAAA,CAACD,iBAAiB,qBAChBhB,KAAA,CAAAiB,aAAA,CAACb,qBAAqB,EAAAc,QAAA,KAChBN,IAAI;IACRC,KAAK,EAAEA,KAAM;IACbE,UAAU,EAAEA,UAAW;IACvBD,WAAW,EAAEA;EAAY,EAC1B,CACgB,CAAC;AAExB;;AAEA;AACA;AACA;AACA;AACA,eAAeb,sBAAsB,CAKnCI,0BAA0B,CAAC", "ignoreList": []}