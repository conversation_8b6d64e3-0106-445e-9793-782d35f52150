{"version": 3, "file": "springUtils.d.ts", "sourceRoot": "", "sources": ["../../../src/animation/springUtils.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,eAAe,EACf,SAAS,EACT,YAAY,EACZ,SAAS,EACV,MAAM,gBAAgB,CAAC;AAGxB;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,MAAM,YAAY,GAAG;IACzB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,yBAAyB,CAAC,EAAE,MAAM,CAAC;IACnC,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,YAAY,CAAC,EAAE,YAAY,CAAC;CAC7B,GAAG,CACA;IACE,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,KAAK,CAAC;IACjB,YAAY,CAAC,EAAE,KAAK,CAAC;IACrB,KAAK,CAAC,EAAE,KAAK,CAAC;CACf,GACD;IACE,IAAI,CAAC,EAAE,KAAK,CAAC;IACb,OAAO,CAAC,EAAE,KAAK,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,KAAK,CAAC,EAAE;QAAE,GAAG,CAAC,EAAE,MAAM,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;CACxC,CACJ,CAAC;AAIF,MAAM,MAAM,mBAAmB,GAAG;KAC/B,CAAC,IAAI,MAAM,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,cAAc,GAAG,OAAO,GACnE,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,GACrC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;CAC9B,CAAC;AACF,MAAM,MAAM,gBAAgB,GAAG,YAAY,CAAC;AAE5C,MAAM,WAAW,iBAAiB;IAChC,WAAW,EAAE,OAAO,CAAC;IACrB,aAAa,EAAE,OAAO,CAAC;CACxB;AAED,MAAM,WAAW,eAAgB,SAAQ,SAAS,CAAC,eAAe,CAAC;IACjE,OAAO,EAAE,eAAe,CAAC;IACzB,OAAO,EAAE,eAAe,CAAC;IACzB,QAAQ,EAAE,MAAM,CAAC;IACjB,aAAa,EAAE,SAAS,CAAC;IACzB,cAAc,EAAE,SAAS,CAAC;IAC1B,UAAU,EAAE,MAAM,CAAC;IACnB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,oBACf,SAAQ,IAAI,CAAC,eAAe,EAAE,SAAS,GAAG,SAAS,CAAC;IACpD,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;CACjB;AACD,wBAAgB,oBAAoB,CAAC,MAAM,EAAE,mBAAmB,GAAG,OAAO,CAoCzE;AAGD,wBAAgB,UAAU,CAAC,EACzB,GAAG,EACH,GAAG,EACH,IAAI,EACJ,aAAkB,GACnB,EAAE;IACD,GAAG,EAAE,MAAM,CAAC;IACZ,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAC5B,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB,UAgBA;AAED,wBAAgB,mBAAmB,CACjC,IAAI,oBAAI,EACR,MAAM,EAAE,mBAAmB,GAAG,iBAAiB,GAC9C;IACD,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;CAChB,CA4BA;AAED;;;;GAIG;AACH,wBAAgB,sBAAsB,CACpC,SAAS,EAAE,eAAe,EAC1B,KAAK,EAAE;IAAE,GAAG,CAAC,EAAE,MAAM,CAAC;IAAC,GAAG,CAAC,EAAE,MAAM,CAAA;CAAE,GACpC,MAAM,CA6DR;AAED,0BAA0B;AAC1B,wBAAgB,+BAA+B,CAC7C,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,mBAAmB,GAAG,iBAAiB,EAC/C,EAAE,EAAE,MAAM,UAgDX;AAED,wBAAgB,kCAAkC,CAChD,SAAS,EAAE,oBAAoB,EAC/B,mBAAmB,EAAE;IACnB,EAAE,EAAE,MAAM,CAAC;IACX,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,MAAM,CAAC;IACf,CAAC,EAAE,MAAM,CAAC;CACX,GACA;IAAE,QAAQ,EAAE,MAAM,CAAC;IAAC,QAAQ,EAAE,MAAM,CAAA;CAAE,CAkBxC;AAED,wBAAgB,6BAA6B,CAC3C,SAAS,EAAE,oBAAoB,EAC/B,mBAAmB,EAAE;IACnB,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,EAAE,MAAM,CAAC;IACX,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,CAAC,EAAE,MAAM,CAAC;CACX,GACA;IAAE,QAAQ,EAAE,MAAM,CAAC;IAAC,QAAQ,EAAE,MAAM,CAAA;CAAE,CA0BxC;AAED,wBAAgB,iCAAiC,CAC/C,SAAS,EAAE,oBAAoB,EAC/B,MAAM,EAAE,mBAAmB,GAC1B;IACD,cAAc,EAAE,OAAO,CAAC;IACxB,UAAU,EAAE,OAAO,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC;CACzB,CAcA"}