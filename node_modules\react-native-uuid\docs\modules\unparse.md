[react-native-uuid](..) / [Exports](../modules.md) / unparse

# Module: unparse

## Table of contents

### Functions

- [unparse](unparse.md#unparse)

## Functions

### unparse

▸ `Const`**unparse**(`buf`: *number*[], `offset?`: *number*): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`buf` | *number*[] |
`offset?` | *number* |

**Returns:** *string*

Defined in: [unparse.ts:5](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/unparse.ts#L5)
