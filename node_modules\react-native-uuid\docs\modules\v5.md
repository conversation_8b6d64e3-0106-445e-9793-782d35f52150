[react-native-uuid](..) / [Exports](../modules.md) / v5

# Module: v5

## Table of contents

### Functions

- [v5](v5.md#v5)

## Functions

### v5

▸ `Const`**v5**(`value`: *string* \| *Uint8Array*, `namespace`: *string* \| *number*[], `buf?`: *number*[], `offset?`: *number*): *string* \| *number*[]

#### Parameters:

Name | Type |
:------ | :------ |
`value` | *string* \| *Uint8Array* |
`namespace` | *string* \| *number*[] |
`buf?` | *number*[] |
`offset?` | *number* |

**Returns:** *string* \| *number*[]

Defined in: [v5.ts:4](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/v5.ts#L4)
