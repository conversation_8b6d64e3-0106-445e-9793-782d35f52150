// Account Service for Database Operations

import { v4 as uuidv4 } from 'react-native-uuid';
import databaseService from '../database';
import { Account } from '../types';

export class AccountService {
  async getAllAccounts(): Promise<Account[]> {
    const query = `
      SELECT * FROM accounts 
      WHERE is_active = 1 
      ORDER BY created_at ASC
    `;
    
    const rows = await databaseService.executeQuery(query);
    return rows.map(this.mapRowToAccount);
  }

  async getAccountById(id: string): Promise<Account | null> {
    const query = 'SELECT * FROM accounts WHERE id = ?';
    const rows = await databaseService.executeQuery(query, [id]);
    return rows.length > 0 ? this.mapRowToAccount(rows[0]) : null;
  }

  async createAccount(account: Omit<Account, 'id' | 'createdAt' | 'updatedAt'>): Promise<Account> {
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const query = `
      INSERT INTO accounts (
        id, name, type, balance, currency, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await databaseService.executeQuery(query, [
      id,
      account.name,
      account.type,
      account.balance,
      account.currency,
      account.isActive ? 1 : 0,
      now,
      now,
    ]);

    const createdAccount = await this.getAccountById(id);
    if (!createdAccount) {
      throw new Error('Failed to create account');
    }

    return createdAccount;
  }

  async updateAccount(id: string, updates: Partial<Account>): Promise<Account> {
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (updates.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(updates.name);
    }
    if (updates.type !== undefined) {
      updateFields.push('type = ?');
      updateValues.push(updates.type);
    }
    if (updates.balance !== undefined) {
      updateFields.push('balance = ?');
      updateValues.push(updates.balance);
    }
    if (updates.currency !== undefined) {
      updateFields.push('currency = ?');
      updateValues.push(updates.currency);
    }
    if (updates.isActive !== undefined) {
      updateFields.push('is_active = ?');
      updateValues.push(updates.isActive ? 1 : 0);
    }

    updateFields.push('updated_at = ?');
    updateValues.push(new Date().toISOString());
    updateValues.push(id);

    const query = `UPDATE accounts SET ${updateFields.join(', ')} WHERE id = ?`;
    await databaseService.executeQuery(query, updateValues);

    const updatedAccount = await this.getAccountById(id);
    if (!updatedAccount) {
      throw new Error('Failed to update account');
    }

    return updatedAccount;
  }

  async deleteAccount(id: string): Promise<void> {
    // Check if account has transactions
    const transactionQuery = 'SELECT COUNT(*) as count FROM transactions WHERE account_id = ?';
    const transactionRows = await databaseService.executeQuery(transactionQuery, [id]);
    
    if (transactionRows[0].count > 0) {
      // Soft delete - mark as inactive
      await this.updateAccount(id, { isActive: false });
    } else {
      // Hard delete if no transactions
      const query = 'DELETE FROM accounts WHERE id = ?';
      await databaseService.executeQuery(query, [id]);
    }
  }

  async getTotalBalance(): Promise<number> {
    const query = `
      SELECT SUM(balance) as total 
      FROM accounts 
      WHERE is_active = 1 AND type != 'credit_card'
    `;
    
    const rows = await databaseService.executeQuery(query);
    return rows[0].total || 0;
  }

  async getAccountBalance(id: string): Promise<number> {
    const account = await this.getAccountById(id);
    return account ? account.balance : 0;
  }

  async updateAccountBalance(id: string, amount: number): Promise<void> {
    const query = `
      UPDATE accounts 
      SET balance = balance + ?, updated_at = ?
      WHERE id = ?
    `;
    
    await databaseService.executeQuery(query, [
      amount,
      new Date().toISOString(),
      id,
    ]);
  }

  async getAccountsByType(type: string): Promise<Account[]> {
    const query = `
      SELECT * FROM accounts 
      WHERE type = ? AND is_active = 1 
      ORDER BY created_at ASC
    `;
    
    const rows = await databaseService.executeQuery(query, [type]);
    return rows.map(this.mapRowToAccount);
  }

  async clearAllAccounts(): Promise<void> {
    const query = 'DELETE FROM accounts';
    await databaseService.executeQuery(query);
  }

  private mapRowToAccount(row: any): Account {
    return {
      id: row.id,
      name: row.name,
      type: row.type,
      balance: row.balance,
      currency: row.currency,
      isActive: Boolean(row.is_active),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}

export const accountService = new AccountService();
