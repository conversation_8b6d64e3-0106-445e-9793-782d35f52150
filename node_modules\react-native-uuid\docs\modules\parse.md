[react-native-uuid](..) / [Exports](../modules.md) / parse

# Module: parse

## Table of contents

### Functions

- [parse](parse.md#parse)

## Functions

### parse

▸ `Const`**parse**(`s`: *string*, `buf?`: *number*[], `offset?`: *number*): *number*[]

#### Parameters:

Name | Type |
:------ | :------ |
`s` | *string* |
`buf?` | *number*[] |
`offset?` | *number* |

**Returns:** *number*[]

Defined in: [parse.ts:4](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/parse.ts#L4)
