// Database Schema for Budget Tracker

export const CREATE_TABLES = {
  USERS: `
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      pin TEXT,
      biometric_enabled INTEGER DEFAULT 0,
      security_question TEXT,
      security_answer TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,

  ACCOUNTS: `
    CREATE TABLE IF NOT EXISTS accounts (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('bank', 'credit_card', 'cash', 'savings')),
      balance REAL DEFAULT 0,
      currency TEXT DEFAULT 'INR',
      is_active INTEGER DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,

  CATEGORIES: `
    CREATE TABLE IF NOT EXISTS categories (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      color TEXT NOT NULL,
      icon TEXT NOT NULL,
      is_default INTEGER DEFAULT 0,
      parent_id TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (parent_id) REFERENCES categories (id)
    );
  `,

  PAYMENT_METHODS: `
    CREATE TABLE IF NOT EXISTS payment_methods (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('cash', 'card', 'upi', 'net_banking', 'wallet')),
      account_id TEXT,
      is_default INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (account_id) REFERENCES accounts (id)
    );
  `,

  TRANSACTIONS: `
    CREATE TABLE IF NOT EXISTS transactions (
      id TEXT PRIMARY KEY,
      amount REAL NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense', 'transfer')),
      category_id TEXT NOT NULL,
      account_id TEXT NOT NULL,
      payment_method_id TEXT NOT NULL,
      description TEXT,
      date DATETIME NOT NULL,
      is_recurring INTEGER DEFAULT 0,
      recurring_id TEXT,
      sms_id TEXT,
      is_confirmed INTEGER DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (category_id) REFERENCES categories (id),
      FOREIGN KEY (account_id) REFERENCES accounts (id),
      FOREIGN KEY (payment_method_id) REFERENCES payment_methods (id),
      FOREIGN KEY (recurring_id) REFERENCES recurring_transactions (id),
      FOREIGN KEY (sms_id) REFERENCES sms_transactions (id)
    );
  `,

  RECURRING_TRANSACTIONS: `
    CREATE TABLE IF NOT EXISTS recurring_transactions (
      id TEXT PRIMARY KEY,
      amount REAL NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      category_id TEXT NOT NULL,
      account_id TEXT NOT NULL,
      payment_method_id TEXT NOT NULL,
      description TEXT,
      frequency TEXT NOT NULL CHECK (frequency IN ('daily', 'weekly', 'monthly', 'yearly')),
      start_date DATETIME NOT NULL,
      end_date DATETIME,
      next_due_date DATETIME NOT NULL,
      is_active INTEGER DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (category_id) REFERENCES categories (id),
      FOREIGN KEY (account_id) REFERENCES accounts (id),
      FOREIGN KEY (payment_method_id) REFERENCES payment_methods (id)
    );
  `,

  BUDGETS: `
    CREATE TABLE IF NOT EXISTS budgets (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      category_id TEXT NOT NULL,
      amount REAL NOT NULL,
      period TEXT NOT NULL CHECK (period IN ('weekly', 'monthly', 'yearly')),
      start_date DATETIME NOT NULL,
      end_date DATETIME NOT NULL,
      spent REAL DEFAULT 0,
      is_active INTEGER DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (category_id) REFERENCES categories (id)
    );
  `,

  INVESTMENTS: `
    CREATE TABLE IF NOT EXISTS investments (
      id TEXT PRIMARY KEY,
      type TEXT NOT NULL CHECK (type IN ('stock', 'mutual_fund', 'fd', 'rd')),
      name TEXT NOT NULL,
      symbol TEXT,
      quantity REAL,
      purchase_price REAL,
      current_price REAL,
      purchase_date DATETIME NOT NULL,
      maturity_date DATETIME,
      interest_rate REAL,
      monthly_installment REAL,
      total_invested REAL NOT NULL,
      current_value REAL DEFAULT 0,
      gain_loss REAL DEFAULT 0,
      gain_loss_percentage REAL DEFAULT 0,
      is_active INTEGER DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,

  SMS_TRANSACTIONS: `
    CREATE TABLE IF NOT EXISTS sms_transactions (
      id TEXT PRIMARY KEY,
      sms_body TEXT NOT NULL,
      sender TEXT NOT NULL,
      received_at DATETIME NOT NULL,
      amount REAL,
      transaction_type TEXT CHECK (transaction_type IN ('debit', 'credit')),
      merchant TEXT,
      account_number TEXT,
      is_processed INTEGER DEFAULT 0,
      transaction_id TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (transaction_id) REFERENCES transactions (id)
    );
  `,

  NOTIFICATIONS: `
    CREATE TABLE IF NOT EXISTS notifications (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      message TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('budget_alert', 'recurring_reminder', 'investment_maturity', 'general')),
      is_read INTEGER DEFAULT 0,
      scheduled_for DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,

  APP_SETTINGS: `
    CREATE TABLE IF NOT EXISTS app_settings (
      id TEXT PRIMARY KEY DEFAULT 'default',
      currency TEXT DEFAULT 'INR',
      date_format TEXT DEFAULT 'DD MMM YYYY',
      theme TEXT DEFAULT 'light' CHECK (theme IN ('light', 'dark', 'auto')),
      notifications INTEGER DEFAULT 1,
      biometric_auth INTEGER DEFAULT 0,
      auto_lock INTEGER DEFAULT 1,
      auto_lock_timeout INTEGER DEFAULT 300000,
      sms_permission INTEGER DEFAULT 0,
      backup_enabled INTEGER DEFAULT 0,
      last_backup_date DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,
};

export const CREATE_INDEXES = {
  TRANSACTIONS_DATE: 'CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions (date);',
  TRANSACTIONS_CATEGORY: 'CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions (category_id);',
  TRANSACTIONS_ACCOUNT: 'CREATE INDEX IF NOT EXISTS idx_transactions_account ON transactions (account_id);',
  BUDGETS_PERIOD: 'CREATE INDEX IF NOT EXISTS idx_budgets_period ON budgets (start_date, end_date);',
  SMS_SENDER: 'CREATE INDEX IF NOT EXISTS idx_sms_sender ON sms_transactions (sender);',
  SMS_PROCESSED: 'CREATE INDEX IF NOT EXISTS idx_sms_processed ON sms_transactions (is_processed);',
  INVESTMENTS_TYPE: 'CREATE INDEX IF NOT EXISTS idx_investments_type ON investments (type);',
  NOTIFICATIONS_TYPE: 'CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications (type);',
};

export const INSERT_DEFAULT_DATA = {
  DEFAULT_ACCOUNT: `
    INSERT OR IGNORE INTO accounts (id, name, type, balance, currency)
    VALUES ('default_cash', 'Cash', 'cash', 0, 'INR');
  `,

  DEFAULT_SETTINGS: `
    INSERT OR IGNORE INTO app_settings (id) VALUES ('default');
  `,
};

export const DROP_TABLES = {
  USERS: 'DROP TABLE IF EXISTS users;',
  ACCOUNTS: 'DROP TABLE IF EXISTS accounts;',
  CATEGORIES: 'DROP TABLE IF EXISTS categories;',
  PAYMENT_METHODS: 'DROP TABLE IF EXISTS payment_methods;',
  TRANSACTIONS: 'DROP TABLE IF EXISTS transactions;',
  RECURRING_TRANSACTIONS: 'DROP TABLE IF EXISTS recurring_transactions;',
  BUDGETS: 'DROP TABLE IF EXISTS budgets;',
  INVESTMENTS: 'DROP TABLE IF EXISTS investments;',
  SMS_TRANSACTIONS: 'DROP TABLE IF EXISTS sms_transactions;',
  NOTIFICATIONS: 'DROP TABLE IF EXISTS notifications;',
  APP_SETTINGS: 'DROP TABLE IF EXISTS app_settings;',
};

// Migration scripts for future versions
export const MIGRATIONS = {
  v1_to_v2: [
    // Add any migration scripts here for future versions
  ],
};
