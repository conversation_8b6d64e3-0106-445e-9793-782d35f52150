{"version": 3, "names": ["version", "reactVersion", "Platform", "isJest", "process", "env", "JEST_WORKER_ID", "isChromeDebugger", "global", "nativeCallSyncHook", "__REMOTEDEV__", "RN$Bridgeless", "isWeb", "OS", "isAndroid", "isWindows", "shouldBeUseWeb", "isF<PERSON><PERSON>", "_IS_FABRIC", "isReact19", "startsWith", "isWindowAvailable", "window"], "sourceRoot": "../../src", "sources": ["PlatformChecker.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,OAAO,IAAIC,YAAY,QAAQ,OAAO;AAC/C,SAASC,QAAQ,QAAQ,cAAc;;AAEvC;AACA;;AAGA,OAAO,SAASC,MAAMA,CAAA,EAAY;EAChC,OAAO,CAAC,CAACC,OAAO,CAACC,GAAG,CAACC,cAAc;AACrC;;AAEA;AACA,OAAO,SAASC,gBAAgBA,CAAA,EAAY;EAC1C,OACE,CAAC,CAAEC,MAAM,CAAiBC,kBAAkB,IAC1C,CAAC,CAAED,MAAM,CAAiBE,aAAa,KACzC,CAAEF,MAAM,CAAiBG,aAAa;AAE1C;AAEA,OAAO,SAASC,KAAKA,CAAA,EAAY;EAC/B,OAAOV,QAAQ,CAACW,EAAE,KAAK,KAAK;AAC9B;AAEA,OAAO,SAASC,SAASA,CAAA,EAAY;EACnC,OAAOZ,QAAQ,CAACW,EAAE,KAAK,SAAS;AAClC;AAEA,SAASE,SAASA,CAAA,EAAY;EAC5B,OAAOb,QAAQ,CAACW,EAAE,KAAK,SAAS;AAClC;AAEA,OAAO,SAASG,cAAcA,CAAA,EAAG;EAC/B,OAAOb,MAAM,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC,IAAIK,KAAK,CAAC,CAAC,IAAIG,SAAS,CAAC,CAAC;AACjE;AAEA,OAAO,SAASE,QAAQA,CAAA,EAAG;EACzB,OAAO,CAAC,CAAET,MAAM,CAAiBU,UAAU;AAC7C;AAEA,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1B,OAAOlB,YAAY,CAACmB,UAAU,CAAC,KAAK,CAAC;AACvC;AAEA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC;EACA;EACA;EACA;EACA,OAAO,OAAOC,MAAM,KAAK,WAAW;AACtC", "ignoreList": []}