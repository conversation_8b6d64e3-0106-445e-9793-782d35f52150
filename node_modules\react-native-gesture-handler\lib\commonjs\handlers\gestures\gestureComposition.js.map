{"version": 3, "sources": ["gestureComposition.ts"], "names": ["extendRelation", "currentRelation", "extendWith", "undefined", "ComposedGesture", "Gesture", "constructor", "gestures", "prepareSingleGesture", "gesture", "simultaneousGestures", "requireGesturesToFail", "BaseGesture", "newConfig", "config", "simultaneousWith", "requireToFail", "prepare", "initialize", "toGestureArray", "flatMap", "SimultaneousGesture", "simultaneousArrays", "map", "filter", "x", "i", "length", "ExclusiveGesture", "gestureArrays", "concat"], "mappings": ";;;;;;;AAAA;;;;AAEA,SAASA,cAAT,CACEC,eADF,EAEEC,UAFF,EAGE;AACA,MAAID,eAAe,KAAKE,SAAxB,EAAmC;AACjC,WAAO,CAAC,GAAGD,UAAJ,CAAP;AACD,GAFD,MAEO;AACL,WAAO,CAAC,GAAGD,eAAJ,EAAqB,GAAGC,UAAxB,CAAP;AACD;AACF;;AAEM,MAAME,eAAN,SAA8BC,gBAA9B,CAAsC;AAK3CC,EAAAA,WAAW,CAAC,GAAGC,QAAJ,EAAyB;AAClC;;AADkC,sCAJJ,EAII;;AAAA,kDAHY,EAGZ;;AAAA,mDAFa,EAEb;;AAElC,SAAKA,QAAL,GAAgBA,QAAhB;AACD;;AAESC,EAAAA,oBAAoB,CAC5BC,OAD4B,EAE5BC,oBAF4B,EAG5BC,qBAH4B,EAI5B;AACA,QAAIF,OAAO,YAAYG,oBAAvB,EAAoC;AAClC,YAAMC,SAAS,GAAG,EAAE,GAAGJ,OAAO,CAACK;AAAb,OAAlB,CADkC,CAGlC;AACA;;AACAD,MAAAA,SAAS,CAACE,gBAAV,GAA6Bf,cAAc,CACzCa,SAAS,CAACE,gBAD+B,EAEzCL,oBAFyC,CAA3C;AAIAG,MAAAA,SAAS,CAACG,aAAV,GAA0BhB,cAAc,CACtCa,SAAS,CAACG,aAD4B,EAEtCL,qBAFsC,CAAxC;AAKAF,MAAAA,OAAO,CAACK,MAAR,GAAiBD,SAAjB;AACD,KAfD,MAeO,IAAIJ,OAAO,YAAYL,eAAvB,EAAwC;AAC7CK,MAAAA,OAAO,CAACC,oBAAR,GAA+BA,oBAA/B;AACAD,MAAAA,OAAO,CAACE,qBAAR,GAAgCA,qBAAhC;AACAF,MAAAA,OAAO,CAACQ,OAAR;AACD;AACF;;AAEDA,EAAAA,OAAO,GAAG;AACR,SAAK,MAAMR,OAAX,IAAsB,KAAKF,QAA3B,EAAqC;AACnC,WAAKC,oBAAL,CACEC,OADF,EAEE,KAAKC,oBAFP,EAGE,KAAKC,qBAHP;AAKD;AACF;;AAEDO,EAAAA,UAAU,GAAG;AACX,SAAK,MAAMT,OAAX,IAAsB,KAAKF,QAA3B,EAAqC;AACnCE,MAAAA,OAAO,CAACS,UAAR;AACD;AACF;;AAEDC,EAAAA,cAAc,GAAkB;AAC9B,WAAO,KAAKZ,QAAL,CAAca,OAAd,CAAuBX,OAAD,IAAaA,OAAO,CAACU,cAAR,EAAnC,CAAP;AACD;;AAvD0C;;;;AA0DtC,MAAME,mBAAN,SAAkCjB,eAAlC,CAAkD;AACvDa,EAAAA,OAAO,GAAG;AACR;AACA;AACA,UAAMK,kBAAkB,GAAG,KAAKf,QAAL,CAAcgB,GAAd,CAAmBd,OAAD,IAC3C;AACA,SAAKF,QAAL,CACE;AADF,KAEGiB,MAFH,CAEWC,CAAD,IAAOA,CAAC,KAAKhB,OAFvB,EAGE;AACA;AACA;AACA;AANF,KAOGW,OAPH,CAOYK,CAAD,IAAOA,CAAC,CAACN,cAAF,EAPlB,CAFyB,CAA3B;;AAYA,SAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKnB,QAAL,CAAcoB,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;AAC7C,WAAKlB,oBAAL,CACE,KAAKD,QAAL,CAAcmB,CAAd,CADF,EAEEJ,kBAAkB,CAACI,CAAD,CAFpB,EAGE,KAAKf,qBAHP;AAKD;AACF;;AAvBsD;;;;AA0BlD,MAAMiB,gBAAN,SAA+BxB,eAA/B,CAA+C;AACpDa,EAAAA,OAAO,GAAG;AACR;AACA;AACA,UAAMY,aAAa,GAAG,KAAKtB,QAAL,CAAcgB,GAAd,CAAmBd,OAAD,IACtCA,OAAO,CAACU,cAAR,EADoB,CAAtB;AAIA,QAAIH,aAA4B,GAAG,EAAnC;;AAEA,SAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKnB,QAAL,CAAcoB,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;AAC7C,WAAKlB,oBAAL,CACE,KAAKD,QAAL,CAAcmB,CAAd,CADF,EAEE,KAAKhB,oBAFP,EAGE,KAAKC,qBAAL,CAA2BmB,MAA3B,CAAkCd,aAAlC,CAHF,EAD6C,CAO7C;;AACAA,MAAAA,aAAa,GAAGA,aAAa,CAACc,MAAd,CAAqBD,aAAa,CAACH,CAAD,CAAlC,CAAhB;AACD;AACF;;AApBmD", "sourcesContent": ["import { BaseGesture, Gesture, GestureRef, GestureType } from './gesture';\n\nfunction extendRelation(\n  currentRelation: GestureRef[] | undefined,\n  extendWith: GestureType[]\n) {\n  if (currentRelation === undefined) {\n    return [...extendWith];\n  } else {\n    return [...currentRelation, ...extendWith];\n  }\n}\n\nexport class ComposedGesture extends Gesture {\n  protected gestures: Gesture[] = [];\n  protected simultaneousGestures: GestureType[] = [];\n  protected requireGesturesToFail: GestureType[] = [];\n\n  constructor(...gestures: Gesture[]) {\n    super();\n    this.gestures = gestures;\n  }\n\n  protected prepareSingleGesture(\n    gesture: Gesture,\n    simultaneousGestures: GestureType[],\n    requireGesturesToFail: GestureType[]\n  ) {\n    if (gesture instanceof BaseGesture) {\n      const newConfig = { ...gesture.config };\n\n      // No need to extend `blocksHandlers` here, because it's not changed in composition.\n      // The same effect is achieved by reversing the order of 2 gestures in `Exclusive`\n      newConfig.simultaneousWith = extendRelation(\n        newConfig.simultaneousWith,\n        simultaneousGestures\n      );\n      newConfig.requireToFail = extendRelation(\n        newConfig.requireToFail,\n        requireGesturesToFail\n      );\n\n      gesture.config = newConfig;\n    } else if (gesture instanceof ComposedGesture) {\n      gesture.simultaneousGestures = simultaneousGestures;\n      gesture.requireGesturesToFail = requireGesturesToFail;\n      gesture.prepare();\n    }\n  }\n\n  prepare() {\n    for (const gesture of this.gestures) {\n      this.prepareSingleGesture(\n        gesture,\n        this.simultaneousGestures,\n        this.requireGesturesToFail\n      );\n    }\n  }\n\n  initialize() {\n    for (const gesture of this.gestures) {\n      gesture.initialize();\n    }\n  }\n\n  toGestureArray(): GestureType[] {\n    return this.gestures.flatMap((gesture) => gesture.toGestureArray());\n  }\n}\n\nexport class SimultaneousGesture extends ComposedGesture {\n  prepare() {\n    // This piece of magic works something like this:\n    // for every gesture in the array\n    const simultaneousArrays = this.gestures.map((gesture) =>\n      // we take the array it's in\n      this.gestures\n        // and make a copy without it\n        .filter((x) => x !== gesture)\n        // then we flatmap the result to get list of raw (not composed) gestures\n        // this way we don't make the gestures simultaneous with themselves, which is\n        // important when the gesture is `ExclusiveGesture` - we don't want to make\n        // exclusive gestures simultaneous\n        .flatMap((x) => x.toGestureArray())\n    );\n\n    for (let i = 0; i < this.gestures.length; i++) {\n      this.prepareSingleGesture(\n        this.gestures[i],\n        simultaneousArrays[i],\n        this.requireGesturesToFail\n      );\n    }\n  }\n}\n\nexport class ExclusiveGesture extends ComposedGesture {\n  prepare() {\n    // Transforms the array of gestures into array of grouped raw (not composed) gestures\n    // i.e. [gesture1, gesture2, ComposedGesture(gesture3, gesture4)] -> [[gesture1], [gesture2], [gesture3, gesture4]]\n    const gestureArrays = this.gestures.map((gesture) =>\n      gesture.toGestureArray()\n    );\n\n    let requireToFail: GestureType[] = [];\n\n    for (let i = 0; i < this.gestures.length; i++) {\n      this.prepareSingleGesture(\n        this.gestures[i],\n        this.simultaneousGestures,\n        this.requireGesturesToFail.concat(requireToFail)\n      );\n\n      // Every group gets to wait for all groups before it\n      requireToFail = requireToFail.concat(gestureArrays[i]);\n    }\n  }\n}\n\nexport type ComposedGestureType = InstanceType<typeof ComposedGesture>;\nexport type RaceGestureType = ComposedGestureType;\nexport type SimultaneousGestureType = InstanceType<typeof SimultaneousGesture>;\nexport type ExclusiveGestureType = InstanceType<typeof ExclusiveGesture>;\n"]}