// Authentication Hook

import { useState, useEffect, useCallback } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { authService, AuthResult } from '../services/AuthService';
import { useAppStore } from '../store';

export interface UseAuthReturn {
  isAuthenticated: boolean;
  isLoading: boolean;
  hasPIN: boolean;
  isBiometricAvailable: boolean;
  isBiometricEnabled: boolean;
  isLocked: boolean;
  authenticate: (pin?: string) => Promise<AuthResult>;
  authenticateWithBiometric: () => Promise<AuthResult>;
  setPIN: (pin: string) => Promise<AuthResult>;
  changePIN: (oldPin: string, newPin: string) => Promise<AuthResult>;
  removePIN: () => Promise<AuthResult>;
  setBiometricEnabled: (enabled: boolean) => Promise<void>;
  setSecurityQuestion: (question: string, answer: string) => Promise<AuthResult>;
  verifySecurityAnswer: (answer: string) => Promise<AuthResult>;
  getSecurityQuestion: () => Promise<string | null>;
  lockApp: () => void;
  unlockApp: () => void;
  logout: () => Promise<void>;
}

export const useAuth = (): UseAuthReturn => {
  const { isAuthenticated, setAuthenticated, updateSettings } = useAppStore();
  const [isLoading, setIsLoading] = useState(true);
  const [hasPIN, setHasPIN] = useState(false);
  const [isBiometricAvailable, setIsBiometricAvailable] = useState(false);
  const [isBiometricEnabled, setIsBiometricEnabledState] = useState(false);
  const [isLocked, setIsLocked] = useState(false);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // Handle app state changes for auto-lock
  useEffect(() => {
    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // App came to foreground
        await authService.setLastActiveTime();
        
        if (isAuthenticated) {
          const shouldLock = await authService.shouldLockApp();
          if (shouldLock) {
            setIsLocked(true);
            setAuthenticated(false);
          }
        }
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        // App went to background
        await authService.setLastActiveTime();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [isAuthenticated, setAuthenticated]);

  const checkAuthStatus = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Check if PIN is set
      const pinExists = await authService.hasPIN();
      setHasPIN(pinExists);

      // Check biometric availability
      const biometricCheck = await authService.isBiometricAvailable();
      setIsBiometricAvailable(biometricCheck.available);

      // Check if biometric is enabled
      const biometricEnabled = await authService.isBiometricEnabled();
      setIsBiometricEnabledState(biometricEnabled);

      // Check if app should be locked
      if (pinExists && isAuthenticated) {
        const shouldLock = await authService.shouldLockApp();
        if (shouldLock) {
          setIsLocked(true);
          setAuthenticated(false);
        }
      }

      // If no PIN is set, user needs to set up authentication
      if (!pinExists) {
        setAuthenticated(false);
        setIsLocked(false);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, setAuthenticated]);

  const authenticate = useCallback(async (pin?: string): Promise<AuthResult> => {
    try {
      if (pin) {
        // Authenticate with PIN
        const result = await authService.verifyPIN(pin);
        if (result.success) {
          setAuthenticated(true);
          setIsLocked(false);
          await authService.setLastActiveTime();
        }
        return result;
      } else {
        // Try biometric first if available and enabled
        if (isBiometricAvailable && isBiometricEnabled) {
          const biometricResult = await authService.authenticateWithBiometric();
          if (biometricResult.success) {
            setAuthenticated(true);
            setIsLocked(false);
            await authService.setLastActiveTime();
            return biometricResult;
          }
        }
        
        // Return indication that PIN is required
        return { success: false, error: 'PIN_REQUIRED' };
      }
    } catch (error) {
      console.error('Authentication error:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }, [isBiometricAvailable, isBiometricEnabled, setAuthenticated]);

  const authenticateWithBiometric = useCallback(async (): Promise<AuthResult> => {
    try {
      const result = await authService.authenticateWithBiometric();
      if (result.success) {
        setAuthenticated(true);
        setIsLocked(false);
        await authService.setLastActiveTime();
      }
      return result;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return { success: false, error: 'Biometric authentication failed' };
    }
  }, [setAuthenticated]);

  const setPIN = useCallback(async (pin: string): Promise<AuthResult> => {
    try {
      const result = await authService.setPIN(pin);
      if (result.success) {
        setHasPIN(true);
        setAuthenticated(true);
        setIsLocked(false);
        await authService.setLastActiveTime();
      }
      return result;
    } catch (error) {
      console.error('Set PIN error:', error);
      return { success: false, error: 'Failed to set PIN' };
    }
  }, [setAuthenticated]);

  const changePIN = useCallback(async (oldPin: string, newPin: string): Promise<AuthResult> => {
    try {
      return await authService.changePIN(oldPin, newPin);
    } catch (error) {
      console.error('Change PIN error:', error);
      return { success: false, error: 'Failed to change PIN' };
    }
  }, []);

  const removePIN = useCallback(async (): Promise<AuthResult> => {
    try {
      const result = await authService.removePIN();
      if (result.success) {
        setHasPIN(false);
        setAuthenticated(false);
        setIsLocked(false);
      }
      return result;
    } catch (error) {
      console.error('Remove PIN error:', error);
      return { success: false, error: 'Failed to remove PIN' };
    }
  }, [setAuthenticated]);

  const setBiometricEnabled = useCallback(async (enabled: boolean): Promise<void> => {
    try {
      await authService.setBiometricEnabled(enabled);
      setIsBiometricEnabledState(enabled);
      updateSettings({ biometricAuth: enabled });
    } catch (error) {
      console.error('Set biometric enabled error:', error);
    }
  }, [updateSettings]);

  const setSecurityQuestion = useCallback(async (question: string, answer: string): Promise<AuthResult> => {
    try {
      return await authService.setSecurityQuestion(question, answer);
    } catch (error) {
      console.error('Set security question error:', error);
      return { success: false, error: 'Failed to set security question' };
    }
  }, []);

  const verifySecurityAnswer = useCallback(async (answer: string): Promise<AuthResult> => {
    try {
      return await authService.verifySecurityAnswer(answer);
    } catch (error) {
      console.error('Verify security answer error:', error);
      return { success: false, error: 'Failed to verify answer' };
    }
  }, []);

  const getSecurityQuestion = useCallback(async (): Promise<string | null> => {
    try {
      return await authService.getSecurityQuestion();
    } catch (error) {
      console.error('Get security question error:', error);
      return null;
    }
  }, []);

  const lockApp = useCallback(() => {
    setIsLocked(true);
    setAuthenticated(false);
  }, [setAuthenticated]);

  const unlockApp = useCallback(() => {
    setIsLocked(false);
    setAuthenticated(true);
  }, [setAuthenticated]);

  const logout = useCallback(async (): Promise<void> => {
    try {
      setAuthenticated(false);
      setIsLocked(false);
      // Note: We don't clear auth data on logout, only on explicit removal
    } catch (error) {
      console.error('Logout error:', error);
    }
  }, [setAuthenticated]);

  return {
    isAuthenticated,
    isLoading,
    hasPIN,
    isBiometricAvailable,
    isBiometricEnabled,
    isLocked,
    authenticate,
    authenticateWithBiometric,
    setPIN,
    changePIN,
    removePIN,
    setBiometricEnabled,
    setSecurityQuestion,
    verifySecurityAnswer,
    getSecurityQuestion,
    lockApp,
    unlockApp,
    logout,
  };
};
