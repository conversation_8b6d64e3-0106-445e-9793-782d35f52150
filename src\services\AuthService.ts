// Authentication Service for PIN and Biometric Authentication

import * as LocalAuthentication from 'expo-local-authentication';
import * as Crypto from 'expo-crypto';
import * as Keychain from 'react-native-keychain';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants';

export interface AuthResult {
  success: boolean;
  error?: string;
  biometricType?: LocalAuthentication.AuthenticationType[];
}

export class AuthService {
  private static readonly PIN_KEY = 'user_pin_hash';
  private static readonly BIOMETRIC_KEY = 'biometric_enabled';
  private static readonly SECURITY_QUESTION_KEY = 'security_question';
  private static readonly SECURITY_ANSWER_KEY = 'security_answer_hash';

  // PIN Authentication
  async setPIN(pin: string): Promise<AuthResult> {
    try {
      if (pin.length < 4 || pin.length > 6) {
        return { success: false, error: 'PIN must be 4-6 digits' };
      }

      if (!/^\d+$/.test(pin)) {
        return { success: false, error: 'PIN must contain only numbers' };
      }

      const hashedPin = await this.hashString(pin);
      
      await Keychain.setInternetCredentials(
        this.PIN_KEY,
        'user',
        hashedPin,
        {
          accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET_OR_DEVICE_PASSCODE,
          authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
        }
      );

      await AsyncStorage.setItem(STORAGE_KEYS.USER_PIN, 'set');
      
      return { success: true };
    } catch (error) {
      console.error('Error setting PIN:', error);
      return { success: false, error: 'Failed to set PIN' };
    }
  }

  async verifyPIN(pin: string): Promise<AuthResult> {
    try {
      const credentials = await Keychain.getInternetCredentials(this.PIN_KEY);
      
      if (!credentials || credentials === false) {
        return { success: false, error: 'No PIN set' };
      }

      const hashedPin = await this.hashString(pin);
      
      if (credentials.password === hashedPin) {
        return { success: true };
      } else {
        return { success: false, error: 'Incorrect PIN' };
      }
    } catch (error) {
      console.error('Error verifying PIN:', error);
      return { success: false, error: 'Failed to verify PIN' };
    }
  }

  async hasPIN(): Promise<boolean> {
    try {
      const pinSet = await AsyncStorage.getItem(STORAGE_KEYS.USER_PIN);
      return pinSet === 'set';
    } catch {
      return false;
    }
  }

  async changePIN(oldPin: string, newPin: string): Promise<AuthResult> {
    const verifyResult = await this.verifyPIN(oldPin);
    if (!verifyResult.success) {
      return { success: false, error: 'Current PIN is incorrect' };
    }

    return await this.setPIN(newPin);
  }

  async removePIN(): Promise<AuthResult> {
    try {
      await Keychain.resetInternetCredentials(this.PIN_KEY);
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_PIN);
      return { success: true };
    } catch (error) {
      console.error('Error removing PIN:', error);
      return { success: false, error: 'Failed to remove PIN' };
    }
  }

  // Biometric Authentication
  async isBiometricAvailable(): Promise<{ available: boolean; types: LocalAuthentication.AuthenticationType[] }> {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();

      return {
        available: hasHardware && isEnrolled,
        types: supportedTypes,
      };
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return { available: false, types: [] };
    }
  }

  async authenticateWithBiometric(reason: string = 'Authenticate to access your budget tracker'): Promise<AuthResult> {
    try {
      const biometricCheck = await this.isBiometricAvailable();
      
      if (!biometricCheck.available) {
        return { success: false, error: 'Biometric authentication not available' };
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: reason,
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use PIN',
        disableDeviceFallback: false,
      });

      if (result.success) {
        return { success: true, biometricType: biometricCheck.types };
      } else {
        return { 
          success: false, 
          error: result.error === 'user_cancel' ? 'Authentication cancelled' : 'Authentication failed' 
        };
      }
    } catch (error) {
      console.error('Error with biometric authentication:', error);
      return { success: false, error: 'Biometric authentication failed' };
    }
  }

  async setBiometricEnabled(enabled: boolean): Promise<void> {
    await AsyncStorage.setItem(STORAGE_KEYS.BIOMETRIC_ENABLED, enabled.toString());
  }

  async isBiometricEnabled(): Promise<boolean> {
    try {
      const enabled = await AsyncStorage.getItem(STORAGE_KEYS.BIOMETRIC_ENABLED);
      return enabled === 'true';
    } catch {
      return false;
    }
  }

  // Security Questions
  async setSecurityQuestion(question: string, answer: string): Promise<AuthResult> {
    try {
      const hashedAnswer = await this.hashString(answer.toLowerCase().trim());
      
      await Keychain.setInternetCredentials(
        this.SECURITY_QUESTION_KEY,
        question,
        hashedAnswer
      );

      return { success: true };
    } catch (error) {
      console.error('Error setting security question:', error);
      return { success: false, error: 'Failed to set security question' };
    }
  }

  async getSecurityQuestion(): Promise<string | null> {
    try {
      const credentials = await Keychain.getInternetCredentials(this.SECURITY_QUESTION_KEY);
      
      if (credentials && credentials !== false) {
        return credentials.username;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting security question:', error);
      return null;
    }
  }

  async verifySecurityAnswer(answer: string): Promise<AuthResult> {
    try {
      const credentials = await Keychain.getInternetCredentials(this.SECURITY_QUESTION_KEY);
      
      if (!credentials || credentials === false) {
        return { success: false, error: 'No security question set' };
      }

      const hashedAnswer = await this.hashString(answer.toLowerCase().trim());
      
      if (credentials.password === hashedAnswer) {
        return { success: true };
      } else {
        return { success: false, error: 'Incorrect answer' };
      }
    } catch (error) {
      console.error('Error verifying security answer:', error);
      return { success: false, error: 'Failed to verify answer' };
    }
  }

  // App Lock Management
  async setAutoLockEnabled(enabled: boolean): Promise<void> {
    await AsyncStorage.setItem('auto_lock_enabled', enabled.toString());
  }

  async isAutoLockEnabled(): Promise<boolean> {
    try {
      const enabled = await AsyncStorage.getItem('auto_lock_enabled');
      return enabled !== 'false'; // Default to true
    } catch {
      return true;
    }
  }

  async setAutoLockTimeout(timeout: number): Promise<void> {
    await AsyncStorage.setItem('auto_lock_timeout', timeout.toString());
  }

  async getAutoLockTimeout(): Promise<number> {
    try {
      const timeout = await AsyncStorage.getItem('auto_lock_timeout');
      return timeout ? parseInt(timeout, 10) : 300000; // Default 5 minutes
    } catch {
      return 300000;
    }
  }

  async setLastActiveTime(): Promise<void> {
    await AsyncStorage.setItem('last_active_time', Date.now().toString());
  }

  async shouldLockApp(): Promise<boolean> {
    try {
      const isAutoLockEnabled = await this.isAutoLockEnabled();
      if (!isAutoLockEnabled) return false;

      const lastActiveTime = await AsyncStorage.getItem('last_active_time');
      if (!lastActiveTime) return true;

      const timeout = await this.getAutoLockTimeout();
      const timeSinceLastActive = Date.now() - parseInt(lastActiveTime, 10);

      return timeSinceLastActive > timeout;
    } catch {
      return true;
    }
  }

  // Utility Methods
  private async hashString(input: string): Promise<string> {
    return await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      input,
      { encoding: Crypto.CryptoEncoding.HEX }
    );
  }

  async clearAllAuthData(): Promise<void> {
    try {
      await Keychain.resetInternetCredentials(this.PIN_KEY);
      await Keychain.resetInternetCredentials(this.SECURITY_QUESTION_KEY);
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.USER_PIN,
        STORAGE_KEYS.BIOMETRIC_ENABLED,
        'auto_lock_enabled',
        'auto_lock_timeout',
        'last_active_time',
      ]);
    } catch (error) {
      console.error('Error clearing auth data:', error);
    }
  }

  // Authentication Flow
  async authenticate(preferBiometric: boolean = true): Promise<AuthResult> {
    try {
      const hasPIN = await this.hasPIN();
      if (!hasPIN) {
        return { success: false, error: 'No authentication method set' };
      }

      const isBiometricEnabled = await this.isBiometricEnabled();
      const biometricAvailable = await this.isBiometricAvailable();

      if (preferBiometric && isBiometricEnabled && biometricAvailable.available) {
        const biometricResult = await this.authenticateWithBiometric();
        if (biometricResult.success) {
          await this.setLastActiveTime();
          return biometricResult;
        }
        // If biometric fails, fall back to PIN
      }

      // Return indication that PIN is needed
      return { success: false, error: 'PIN_REQUIRED' };
    } catch (error) {
      console.error('Error in authentication flow:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }
}

export const authService = new AuthService();
