[react-native-uuid](..) / [Exports](../modules.md) / utils

# Module: utils

## Table of contents

### Variables

- [DNS](utils.md#dns)
- [NIL](utils.md#nil)
- [OID](utils.md#oid)
- [URL](utils.md#url)
- [X500](utils.md#x500)
- [byteToHex](utils.md#bytetohex)
- [hexToByte](utils.md#hextobyte)

### Functions

- [bytesToString](utils.md#bytestostring)
- [stringToBytes](utils.md#stringtobytes)

## Variables

### DNS

• `Const` **DNS**: *6ba7b810-9dad-11d1-80b4-00c04fd430c8*= '6ba7b810-9dad-11d1-80b4-00c04fd430c8'

Defined in: [utils.ts:12](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/utils.ts#L12)

___

### NIL

• `Const` **NIL**: *00000000-0000-0000-0000-000000000000*= '00000000-0000-0000-0000-000000000000'

Defined in: [utils.ts:16](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/utils.ts#L16)

___

### OID

• `Const` **OID**: *6ba7b812-9dad-11d1-80b4-00c04fd430c8*= '6ba7b812-9dad-11d1-80b4-00c04fd430c8'

Defined in: [utils.ts:14](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/utils.ts#L14)

___

### URL

• `Const` **URL**: *6ba7b811-9dad-11d1-80b4-00c04fd430c8*= '6ba7b811-9dad-11d1-80b4-00c04fd430c8'

Defined in: [utils.ts:13](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/utils.ts#L13)

___

### X500

• `Const` **X500**: *6ba7b814-9dad-11d1-80b4-00c04fd430c8*= '6ba7b814-9dad-11d1-80b4-00c04fd430c8'

Defined in: [utils.ts:15](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/utils.ts#L15)

___

### byteToHex

• `Const` **byteToHex**: *string*[]

Defined in: [utils.ts:9](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/utils.ts#L9)

___

### hexToByte

• `Const` **hexToByte**: *object*

#### Type declaration:

Defined in: [utils.ts:10](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/utils.ts#L10)

## Functions

### bytesToString

▸ `Const`**bytesToString**(`buf`: ArrayBuffer): *string*

#### Parameters:

Name | Type |
:------ | :------ |
`buf` | ArrayBuffer |

**Returns:** *string*

Defined in: [utils.ts:30](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/utils.ts#L30)

___

### stringToBytes

▸ `Const`**stringToBytes**(`str`: *string*): *Uint8Array*

#### Parameters:

Name | Type |
:------ | :------ |
`str` | *string* |

**Returns:** *Uint8Array*

Defined in: [utils.ts:18](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/utils.ts#L18)
