import type { TAny } from '../any/index.mjs';
import type { TArray } from '../array/index.mjs';
import type { TAsyncIterator } from '../async-iterator/index.mjs';
import type { TBigInt } from '../bigint/index.mjs';
import type { TBoolean } from '../boolean/index.mjs';
import type { TConstructor } from '../constructor/index.mjs';
import type { TDate } from '../date/index.mjs';
import type { TEnum } from '../enum/index.mjs';
import type { TFunction } from '../function/index.mjs';
import type { TInteger } from '../integer/index.mjs';
import type { TIntersect } from '../intersect/index.mjs';
import type { TIterator } from '../iterator/index.mjs';
import type { TLiteral } from '../literal/index.mjs';
import type { TNot } from '../not/index.mjs';
import type { TNull } from '../null/index.mjs';
import type { TNumber } from '../number/index.mjs';
import type { TObject } from '../object/index.mjs';
import type { TPromise } from '../promise/index.mjs';
import type { TRecord } from '../record/index.mjs';
import type { TThis } from '../recursive/index.mjs';
import type { TRef } from '../ref/index.mjs';
import type { TRegExp } from '../regexp/index.mjs';
import type { TString } from '../string/index.mjs';
import type { TSymbol } from '../symbol/index.mjs';
import type { TTemplateLiteral } from '../template-literal/index.mjs';
import type { TTuple } from '../tuple/index.mjs';
import type { TUint8Array } from '../uint8array/index.mjs';
import type { TUndefined } from '../undefined/index.mjs';
import type { TUnion } from '../union/index.mjs';
import type { TUnknown } from '../unknown/index.mjs';
import type { TVoid } from '../void/index.mjs';
import type { TSchema } from './schema.mjs';
export type TAnySchema = TSchema | TAny | TArray | TAsyncIterator | TBigInt | TBoolean | TConstructor | TDate | TEnum | TFunction | TInteger | TIntersect | TIterator | TLiteral | TNot | TNull | TNumber | TObject | TPromise | TRecord | TRef | TRegExp | TString | TSymbol | TTemplateLiteral | TThis | TTuple | TUndefined | TUnion | TUint8Array | TUnknown | TVoid;
