// Dashboard Screen - Main overview of finances

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants';

interface DashboardScreenProps {
  navigation: any;
}

const DashboardScreen: React.FC<DashboardScreenProps> = ({ navigation }) => {
  // Placeholder data - will be replaced with real data from store
  const totalBalance = 125000;
  const monthlyIncome = 75000;
  const monthlyExpenses = 45000;
  const savings = monthlyIncome - monthlyExpenses;

  const quickActions = [
    {
      title: 'Add Income',
      icon: 'add-circle',
      color: COLORS.SUCCESS,
      onPress: () => navigation.navigate('AddTransaction', { type: 'income' }),
    },
    {
      title: 'Add Expense',
      icon: 'remove-circle',
      color: COLORS.ERROR,
      onPress: () => navigation.navigate('AddTransaction', { type: 'expense' }),
    },
    {
      title: 'Add Budget',
      icon: 'pie-chart',
      color: COLORS.WARNING,
      onPress: () => navigation.navigate('AddBudget'),
    },
    {
      title: 'Add Investment',
      icon: 'trending-up',
      color: COLORS.INFO,
      onPress: () => navigation.navigate('AddInvestment'),
    },
  ];

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Balance Card */}
        <View style={styles.balanceCard}>
          <Text style={styles.balanceLabel}>Total Balance</Text>
          <Text style={styles.balanceAmount}>{formatCurrency(totalBalance)}</Text>
          <View style={styles.balanceDetails}>
            <View style={styles.balanceItem}>
              <Text style={styles.balanceItemLabel}>This Month</Text>
              <Text style={[styles.balanceItemAmount, { color: COLORS.SUCCESS }]}>
                +{formatCurrency(savings)}
              </Text>
            </View>
          </View>
        </View>

        {/* Monthly Overview */}
        <View style={styles.overviewCard}>
          <Text style={styles.cardTitle}>This Month</Text>
          <View style={styles.overviewRow}>
            <View style={styles.overviewItem}>
              <View style={[styles.overviewIcon, { backgroundColor: COLORS.SUCCESS + '20' }]}>
                <Ionicons name="arrow-down" size={20} color={COLORS.SUCCESS} />
              </View>
              <Text style={styles.overviewLabel}>Income</Text>
              <Text style={[styles.overviewAmount, { color: COLORS.SUCCESS }]}>
                {formatCurrency(monthlyIncome)}
              </Text>
            </View>
            <View style={styles.overviewItem}>
              <View style={[styles.overviewIcon, { backgroundColor: COLORS.ERROR + '20' }]}>
                <Ionicons name="arrow-up" size={20} color={COLORS.ERROR} />
              </View>
              <Text style={styles.overviewLabel}>Expenses</Text>
              <Text style={[styles.overviewAmount, { color: COLORS.ERROR }]}>
                {formatCurrency(monthlyExpenses)}
              </Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsCard}>
          <Text style={styles.cardTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <TouchableOpacity
                key={index}
                style={styles.quickActionItem}
                onPress={action.onPress}
              >
                <View style={[styles.quickActionIcon, { backgroundColor: action.color + '20' }]}>
                  <Ionicons name={action.icon as any} size={24} color={action.color} />
                </View>
                <Text style={styles.quickActionText}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recent Transactions */}
        <View style={styles.recentCard}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Recent Transactions</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Transactions')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.emptyState}>
            <Ionicons name="receipt-outline" size={48} color={COLORS.TEXT_SECONDARY} />
            <Text style={styles.emptyStateText}>No transactions yet</Text>
            <Text style={styles.emptyStateSubtext}>
              Add your first transaction to get started
            </Text>
          </View>
        </View>

        {/* Budget Overview */}
        <View style={styles.budgetCard}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Budget Overview</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Budgets')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.emptyState}>
            <Ionicons name="pie-chart-outline" size={48} color={COLORS.TEXT_SECONDARY} />
            <Text style={styles.emptyStateText}>No budgets set</Text>
            <Text style={styles.emptyStateSubtext}>
              Create budgets to track your spending
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  balanceCard: {
    backgroundColor: COLORS.PRIMARY,
    borderRadius: 16,
    padding: 24,
    marginTop: 16,
    marginBottom: 16,
  },
  balanceLabel: {
    fontSize: 16,
    color: COLORS.WHITE + 'CC',
    marginBottom: 8,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: COLORS.WHITE,
    marginBottom: 16,
  },
  balanceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  balanceItem: {
    flex: 1,
  },
  balanceItemLabel: {
    fontSize: 14,
    color: COLORS.WHITE + 'CC',
    marginBottom: 4,
  },
  balanceItemAmount: {
    fontSize: 18,
    fontWeight: '600',
  },
  overviewCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 16,
  },
  overviewRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  overviewItem: {
    flex: 1,
    alignItems: 'center',
  },
  overviewIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  overviewLabel: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 4,
  },
  overviewAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  quickActionsCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionItem: {
    width: '48%',
    alignItems: 'center',
    paddingVertical: 16,
    marginBottom: 8,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 14,
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
  },
  recentCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
  },
  budgetCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: 20,
    marginBottom: 32,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: COLORS.PRIMARY,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 4,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
});

export default DashboardScreen;
