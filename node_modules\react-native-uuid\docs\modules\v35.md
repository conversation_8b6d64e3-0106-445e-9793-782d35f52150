[react-native-uuid](..) / [Exports](../modules.md) / v35

# Module: v35

## Table of contents

### Type aliases

- [GenerateUUID](v35.md#generateuuid)

### Functions

- [v35](v35.md#v35)

## Type aliases

### GenerateUUID

Ƭ **GenerateUUID**: (`value`: *string* \| Uint8Array, `namespace`: *string* \| *number*[], `buf?`: *number*[], `offset?`: *number*) => *string* \| *number*[]

#### Type declaration:

▸ (`value`: *string* \| Uint8Array, `namespace`: *string* \| *number*[], `buf?`: *number*[], `offset?`: *number*): *string* \| *number*[]

#### Parameters:

Name | Type |
:------ | :------ |
`value` | *string* \| Uint8Array |
`namespace` | *string* \| *number*[] |
`buf?` | *number*[] |
`offset?` | *number* |

**Returns:** *string* \| *number*[]

Defined in: [v35.ts:6](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/v35.ts#L6)

## Functions

### v35

▸ `Const`**v35**(`name`: *string*, `version`: *number*, `hashfunc`: (`s`: *string*) => *string*): [*GenerateUUID*](v35.md#generateuuid)

#### Parameters:

Name | Type |
:------ | :------ |
`name` | *string* |
`version` | *number* |
`hashfunc` | (`s`: *string*) => *string* |

**Returns:** [*GenerateUUID*](v35.md#generateuuid)

Defined in: [v35.ts:13](https://github.com/eugenehp/react-native-uuid/blob/d476a1f/src/v35.ts#L13)
