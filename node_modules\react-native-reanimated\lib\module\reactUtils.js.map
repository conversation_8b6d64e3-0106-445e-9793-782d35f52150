{"version": 3, "names": ["React", "forwardRef", "isReact19", "IS_REACT_19", "getCurrentReactOwner", "__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "A", "get<PERSON>wner", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "ReactCurrentOwner", "current", "__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "isReactRendering", "isFirstReactRender", "current<PERSON>wner", "alternate", "componentWithRef", "render", "ref", "props"], "sourceRoot": "../../src", "sources": ["reactUtils.tsx"], "mappings": "AAAA,YAAY;;AAOZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AAEzC,SAASC,SAAS,QAAQ,sBAAmB;AAE7C,MAAMC,WAAW,GAAGD,SAAS,CAAC,CAAC;AAE/B,SAASE,oBAAoBA,CAAA,EAAG;EAC9B;IACE;IACAJ,KAAK,CAACK,+DAA+D,EAAEC,CAAC,EAAEC,QAAQ,GAAG,CAAC;IACtF;IACAP,KAAK,CAACQ,kDAAkD,EAAEC,iBAAiB,EACvEC,OAAO;IACX;IACAV,KAAK,CAACW,+DAA+D,EACjEF,iBAAiB,EAAEC;EAAO;AAElC;AAEA,OAAO,SAASE,gBAAgBA,CAAA,EAAG;EACjC,OAAO,CAAC,CAACR,oBAAoB,CAAC,CAAC;AACjC;AAEA,OAAO,SAASS,kBAAkBA,CAAA,EAAG;EACnC,MAAMC,YAAY,GAAGV,oBAAoB,CAAC,CAAC;EAC3C;EACA;EACA,OAAOU,YAAY,IAAI,CAACA,YAAY,EAAEC,SAAS;AACjD;;AAEA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAC9BC,MAAsC,EACW;EACjD,IAAId,WAAW,EAAE;IACf,OAAQ,CAAC;MAAEe,GAAG;MAAE,GAAGC;IAAM,CAAC,KACxBF,MAAM,CACJE,KAAK,EACLD,GACF,CAAC;EACL;EAEA,OAAOjB,UAAU,CACfgB,MACF,CAAC;AACH", "ignoreList": []}