{"version": 3, "sources": ["GenericTouchable.tsx"], "names": ["TOUCHABLE_STATE", "UNDETERMINED", "BEGAN", "MOVED_OUTSIDE", "GenericTouchable", "Component", "nativeEvent", "pointerInside", "onMoveIn", "onMoveOut", "state", "State", "CANCELLED", "FAILED", "moveToState", "Platform", "OS", "ACTIVE", "STATE", "handlePressIn", "END", "shouldCallOnPress", "longPressDetected", "pressOutTimeout", "handleGoToUndetermined", "props", "onPress", "onLongPress", "delayPressIn", "pressInTimeout", "setTimeout", "time", "delayLongPress", "longPressTimeout", "onLongPressDetected", "handleMoveOutside", "delayPressOut", "clearTimeout", "componentDidMount", "reset", "newState", "onPressIn", "onPressOut", "onStateChange", "componentWillUnmount", "render", "hitSlop", "top", "left", "bottom", "right", "undefined", "coreProps", "accessible", "accessibilityLabel", "accessibilityHint", "accessibilityRole", "accessibilityState", "accessibilityActions", "onAccessibilityAction", "nativeID", "onLayout", "containerStyle", "disabled", "onHandlerStateChange", "onGestureEvent", "userSelect", "shouldActivateOnStart", "disallowInterruption", "testID", "touchSoundDisabled", "extraButtonProps", "style", "children", "rippleColor", "exclusive"], "mappings": ";;;;;;;AAAA;;AAEA;;AAEA;;AACA;;;;;;;;;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMA,eAAe,GAAG;AAC7BC,EAAAA,YAAY,EAAE,CADe;AAE7BC,EAAAA,KAAK,EAAE,CAFsB;AAG7BC,EAAAA,aAAa,EAAE;AAHc,CAAxB;;;AAgBP;AACA;AACA;AACA;AAEe,MAAMC,gBAAN,SAA+BC,eAA/B,CAEb;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,+CAeoB,KAfpB;;AAAA,2CAiBgB,IAjBhB;;AAAA,mCAoBwBL,eAAe,CAACC,YApBxC;;AAAA,4CAkHiB,CAAC;AAChBK,MAAAA,WAAW,EAAE;AAAEC,QAAAA;AAAF;AADG,KAAD,KAEoC;AACnD,UAAI,KAAKA,aAAL,KAAuBA,aAA3B,EAA0C;AACxC,YAAIA,aAAJ,EAAmB;AACjB,eAAKC,QAAL;AACD,SAFD,MAEO;AACL,eAAKC,SAAL;AACD;AACF;;AACD,WAAKF,aAAL,GAAqBA,aAArB;AACD,KA7HD;;AAAA,kDA+HuB,CAAC;AACtBD,MAAAA;AADsB,KAAD,KAEyC;AAC9D,YAAM;AAAEI,QAAAA;AAAF,UAAYJ,WAAlB;;AACA,UAAII,KAAK,KAAKC,aAAMC,SAAhB,IAA6BF,KAAK,KAAKC,aAAME,MAAjD,EAAyD;AACvD;AACA,aAAKC,WAAL,CAAiBd,eAAe,CAACC,YAAjC;AACD,OAHD,MAGO,KACL;AACA;AACA;AACAS,MAAAA,KAAK,MAAMK,sBAASC,EAAT,KAAgB,SAAhB,GAA4BL,aAAMM,MAAlC,GAA2CN,aAAMT,KAAvD,CAAL,IACA,KAAKgB,KAAL,KAAelB,eAAe,CAACC,YAL1B,EAML;AACA;AACA,aAAKkB,aAAL;AACD,OATM,MASA,IAAIT,KAAK,KAAKC,aAAMS,GAApB,EAAyB;AAC9B,cAAMC,iBAAiB,GACrB,CAAC,KAAKC,iBAAN,IACA,KAAKJ,KAAL,KAAelB,eAAe,CAACG,aAD/B,IAEA,KAAKoB,eAAL,KAAyB,IAH3B;AAIA,aAAKC,sBAAL;;AACA,YAAIH,iBAAJ,EAAuB;AAAA;;AACrB;AACA,qDAAKI,KAAL,EAAWC,OAAX;AACD;AACF;AACF,KA1JD;;AAAA,iDA4JsB,MAAM;AAAA;;AAC1B,WAAKJ,iBAAL,GAAyB,IAAzB,CAD0B,CAE1B;;AACA,oDAAKG,KAAL,EAAWE,WAAX;AACD,KAhKD;AAAA;;AAsBA;AACA;AACAR,EAAAA,aAAa,GAAG;AACd,QAAI,KAAKM,KAAL,CAAWG,YAAf,EAA6B;AAC3B,WAAKC,cAAL,GAAsBC,UAAU,CAAC,MAAM;AACrC,aAAKhB,WAAL,CAAiBd,eAAe,CAACE,KAAjC;AACA,aAAK2B,cAAL,GAAsB,IAAtB;AACD,OAH+B,EAG7B,KAAKJ,KAAL,CAAWG,YAHkB,CAAhC;AAID,KALD,MAKO;AACL,WAAKd,WAAL,CAAiBd,eAAe,CAACE,KAAjC;AACD;;AACD,QAAI,KAAKuB,KAAL,CAAWE,WAAf,EAA4B;AAC1B,YAAMI,IAAI,GACR,CAAC,KAAKN,KAAL,CAAWG,YAAX,IAA2B,CAA5B,KAAkC,KAAKH,KAAL,CAAWO,cAAX,IAA6B,CAA/D,CADF;AAEA,WAAKC,gBAAL,GAAwBH,UAAU,CAAC,KAAKI,mBAAN,EAA2BH,IAA3B,CAAlC;AACD;AACF,GAtCD,CAuCA;AACA;;;AACAI,EAAAA,iBAAiB,GAAG;AAClB,QAAI,KAAKV,KAAL,CAAWW,aAAf,EAA8B;AAC5B,WAAKb,eAAL,GACE,KAAKA,eAAL,IACAO,UAAU,CAAC,MAAM;AACf,aAAKhB,WAAL,CAAiBd,eAAe,CAACG,aAAjC;AACA,aAAKoB,eAAL,GAAuB,IAAvB;AACD,OAHS,EAGP,KAAKE,KAAL,CAAWW,aAHJ,CAFZ;AAMD,KAPD,MAOO;AACL,WAAKtB,WAAL,CAAiBd,eAAe,CAACG,aAAjC;AACD;AACF,GApDD,CAsDA;;;AACAqB,EAAAA,sBAAsB,GAAG;AACvBa,IAAAA,YAAY,CAAC,KAAKd,eAAN,CAAZ,CADuB,CACc;;AACrC,QAAI,KAAKE,KAAL,CAAWW,aAAf,EAA8B;AAC5B,WAAKb,eAAL,GAAuBO,UAAU,CAAC,MAAM;AACtC,YAAI,KAAKZ,KAAL,KAAelB,eAAe,CAACC,YAAnC,EAAiD;AAC/C,eAAKa,WAAL,CAAiBd,eAAe,CAACE,KAAjC;AACD;;AACD,aAAKY,WAAL,CAAiBd,eAAe,CAACC,YAAjC;AACA,aAAKsB,eAAL,GAAuB,IAAvB;AACD,OANgC,EAM9B,KAAKE,KAAL,CAAWW,aANmB,CAAjC;AAOD,KARD,MAQO;AACL,UAAI,KAAKlB,KAAL,KAAelB,eAAe,CAACC,YAAnC,EAAiD;AAC/C,aAAKa,WAAL,CAAiBd,eAAe,CAACE,KAAjC;AACD;;AACD,WAAKY,WAAL,CAAiBd,eAAe,CAACC,YAAjC;AACD;AACF;;AAEDqC,EAAAA,iBAAiB,GAAG;AAClB,SAAKC,KAAL;AACD,GA3ED,CA4EA;;;AACAA,EAAAA,KAAK,GAAG;AACN,SAAKjB,iBAAL,GAAyB,KAAzB;AACA,SAAKf,aAAL,GAAqB,IAArB;AACA8B,IAAAA,YAAY,CAAC,KAAKR,cAAN,CAAZ;AACAQ,IAAAA,YAAY,CAAC,KAAKd,eAAN,CAAZ;AACAc,IAAAA,YAAY,CAAC,KAAKJ,gBAAN,CAAZ;AACA,SAAKV,eAAL,GAAuB,IAAvB;AACA,SAAKU,gBAAL,GAAwB,IAAxB;AACA,SAAKJ,cAAL,GAAsB,IAAtB;AACD,GAtFD,CAwFA;;;AACAf,EAAAA,WAAW,CAAC0B,QAAD,EAA2B;AAAA;;AACpC,QAAIA,QAAQ,KAAK,KAAKtB,KAAtB,EAA6B;AAC3B;AACA;AACD;;AACD,QAAIsB,QAAQ,KAAKxC,eAAe,CAACE,KAAjC,EAAwC;AAAA;;AACtC;AACA,oDAAKuB,KAAL,EAAWgB,SAAX;AACD,KAHD,MAGO,IAAID,QAAQ,KAAKxC,eAAe,CAACG,aAAjC,EAAgD;AAAA;;AACrD;AACA,oDAAKsB,KAAL,EAAWiB,UAAX;AACD,KAHM,MAGA,IAAIF,QAAQ,KAAKxC,eAAe,CAACC,YAAjC,EAA+C;AACpD;AACA,WAAKsC,KAAL;;AACA,UAAI,KAAKrB,KAAL,KAAelB,eAAe,CAACE,KAAnC,EAA0C;AAAA;;AACxC;AACA,uDAAKuB,KAAL,EAAWiB,UAAX;AACD;AACF,KAlBmC,CAmBpC;;;AACA,kDAAKjB,KAAL,EAAWkB,aAAX,mGAA2B,KAAKzB,KAAhC,EAAuCsB,QAAvC,EApBoC,CAqBpC;;AACA,SAAKtB,KAAL,GAAasB,QAAb;AACD;;AAkDDI,EAAAA,oBAAoB,GAAG;AACrB;AACA,SAAKL,KAAL;AACD;;AAED/B,EAAAA,QAAQ,GAAG;AACT,QAAI,KAAKU,KAAL,KAAelB,eAAe,CAACG,aAAnC,EAAkD;AAChD;AACA,WAAKW,WAAL,CAAiBd,eAAe,CAACE,KAAjC;AACD;AACF;;AAEDO,EAAAA,SAAS,GAAG;AACV;AACA4B,IAAAA,YAAY,CAAC,KAAKJ,gBAAN,CAAZ;AACA,SAAKA,gBAAL,GAAwB,IAAxB;;AACA,QAAI,KAAKf,KAAL,KAAelB,eAAe,CAACE,KAAnC,EAA0C;AACxC,WAAKiC,iBAAL;AACD;AACF;;AAEDU,EAAAA,MAAM,GAAG;AAAA;;AACP,UAAMC,OAAO,WACV,OAAO,KAAKrB,KAAL,CAAWqB,OAAlB,KAA8B,QAA9B,GACG;AACEC,MAAAA,GAAG,EAAE,KAAKtB,KAAL,CAAWqB,OADlB;AAEEE,MAAAA,IAAI,EAAE,KAAKvB,KAAL,CAAWqB,OAFnB;AAGEG,MAAAA,MAAM,EAAE,KAAKxB,KAAL,CAAWqB,OAHrB;AAIEI,MAAAA,KAAK,EAAE,KAAKzB,KAAL,CAAWqB;AAJpB,KADH,GAOG,KAAKrB,KAAL,CAAWqB,OARJ,uCAQgBK,SAR7B;AAUA,UAAMC,SAAS,GAAG;AAChBC,MAAAA,UAAU,EAAE,KAAK5B,KAAL,CAAW4B,UAAX,KAA0B,KADtB;AAEhBC,MAAAA,kBAAkB,EAAE,KAAK7B,KAAL,CAAW6B,kBAFf;AAGhBC,MAAAA,iBAAiB,EAAE,KAAK9B,KAAL,CAAW8B,iBAHd;AAIhBC,MAAAA,iBAAiB,EAAE,KAAK/B,KAAL,CAAW+B,iBAJd;AAKhB;AACA;AACAC,MAAAA,kBAAkB,EAAE,KAAKhC,KAAL,CAAWgC,kBAPf;AAQhBC,MAAAA,oBAAoB,EAAE,KAAKjC,KAAL,CAAWiC,oBARjB;AAShBC,MAAAA,qBAAqB,EAAE,KAAKlC,KAAL,CAAWkC,qBATlB;AAUhBC,MAAAA,QAAQ,EAAE,KAAKnC,KAAL,CAAWmC,QAVL;AAWhBC,MAAAA,QAAQ,EAAE,KAAKpC,KAAL,CAAWoC;AAXL,KAAlB;AAcA,wBACE,oBAAC,0BAAD;AACE,MAAA,KAAK,EAAE,KAAKpC,KAAL,CAAWqC,cADpB;AAEE,MAAA,oBAAoB,EAClB;AACA,WAAKrC,KAAL,CAAWsC,QAAX,GAAsBZ,SAAtB,GAAkC,KAAKa,oBAJ3C;AAME,MAAA,cAAc,EAAE,KAAKC,cANvB;AAOE,MAAA,OAAO,EAAEnB,OAPX;AAQE,MAAA,UAAU,EAAE,KAAKrB,KAAL,CAAWyC,UARzB;AASE,MAAA,qBAAqB,EAAE,KAAKzC,KAAL,CAAW0C,qBATpC;AAUE,MAAA,oBAAoB,EAAE,KAAK1C,KAAL,CAAW2C,oBAVnC;AAWE,MAAA,MAAM,EAAE,KAAK3C,KAAL,CAAW4C,MAXrB;AAYE,MAAA,kBAAkB,2BAAE,KAAK5C,KAAL,CAAW6C,kBAAb,yEAAmC,KAZvD;AAaE,MAAA,OAAO,EAAE,CAAC,KAAK7C,KAAL,CAAWsC;AAbvB,OAcM,KAAKtC,KAAL,CAAW8C,gBAdjB,gBAeE,oBAAC,qBAAD,CAAU,IAAV,eAAmBnB,SAAnB;AAA8B,MAAA,KAAK,EAAE,KAAK3B,KAAL,CAAW+C;AAAhD,QACG,KAAK/C,KAAL,CAAWgD,QADd,CAfF,CADF;AAqBD;;AArOD;;;;gBAFmBrE,gB,kBAGG;AACpB4B,EAAAA,cAAc,EAAE,GADI;AAEpBuC,EAAAA,gBAAgB,EAAE;AAChBG,IAAAA,WAAW,EAAE,aADG;AAEhBC,IAAAA,SAAS,EAAE;AAFK;AAFE,C", "sourcesContent": ["import * as React from 'react';\nimport { Component } from 'react';\nimport { Animated, Platform } from 'react-native';\n\nimport { State } from '../../State';\nimport { BaseButton } from '../GestureButtons';\n\nimport {\n  GestureEvent,\n  HandlerStateChangeEvent,\n} from '../../handlers/gestureHandlerCommon';\nimport type { NativeViewGestureHandlerPayload } from '../../handlers/GestureHandlerEventPayload';\nimport type { GenericTouchableProps } from './GenericTouchableProps';\n\n/**\n * Each touchable is a states' machine which preforms transitions.\n * On very beginning (and on the very end or recognition) touchable is\n * UNDETERMINED. Then it moves to BEGAN. If touchable recognizes that finger\n * travel outside it transits to special MOVED_OUTSIDE state. Gesture recognition\n * finishes in UNDETERMINED state.\n */\nexport const TOUCHABLE_STATE = {\n  UNDETERMINED: 0,\n  BEGAN: 1,\n  MOVED_OUTSIDE: 2,\n} as const;\n\ntype TouchableState = (typeof TOUCHABLE_STATE)[keyof typeof TOUCHABLE_STATE];\n\ninterface InternalProps {\n  onStateChange?: (oldState: TouchableState, newState: TouchableState) => void;\n}\n\n// TODO: maybe can be better\n// TODO: all clearTimeout have ! added, maybe they shouldn't ?\ntype Timeout = ReturnType<typeof setTimeout> | null | undefined;\n\n/**\n * GenericTouchable is not intented to be used as it is.\n * Should be treated as a source for the rest of touchables\n */\n\nexport default class GenericTouchable extends Component<\n  GenericTouchableProps & InternalProps\n> {\n  static defaultProps = {\n    delayLongPress: 600,\n    extraButtonProps: {\n      rippleColor: 'transparent',\n      exclusive: true,\n    },\n  };\n\n  // Timeout handlers\n  pressInTimeout: Timeout;\n  pressOutTimeout: Timeout;\n  longPressTimeout: Timeout;\n\n  // This flag is required since recognition of longPress implies not-invoking onPress\n  longPressDetected = false;\n\n  pointerInside = true;\n\n  // State of touchable\n  STATE: TouchableState = TOUCHABLE_STATE.UNDETERMINED;\n\n  // handlePressIn in called on first touch on traveling inside component.\n  // Handles state transition with delay.\n  handlePressIn() {\n    if (this.props.delayPressIn) {\n      this.pressInTimeout = setTimeout(() => {\n        this.moveToState(TOUCHABLE_STATE.BEGAN);\n        this.pressInTimeout = null;\n      }, this.props.delayPressIn);\n    } else {\n      this.moveToState(TOUCHABLE_STATE.BEGAN);\n    }\n    if (this.props.onLongPress) {\n      const time =\n        (this.props.delayPressIn || 0) + (this.props.delayLongPress || 0);\n      this.longPressTimeout = setTimeout(this.onLongPressDetected, time);\n    }\n  }\n  // handleMoveOutside in called on traveling outside component.\n  // Handles state transition with delay.\n  handleMoveOutside() {\n    if (this.props.delayPressOut) {\n      this.pressOutTimeout =\n        this.pressOutTimeout ||\n        setTimeout(() => {\n          this.moveToState(TOUCHABLE_STATE.MOVED_OUTSIDE);\n          this.pressOutTimeout = null;\n        }, this.props.delayPressOut);\n    } else {\n      this.moveToState(TOUCHABLE_STATE.MOVED_OUTSIDE);\n    }\n  }\n\n  // handleGoToUndetermined transits to UNDETERMINED state with proper delay\n  handleGoToUndetermined() {\n    clearTimeout(this.pressOutTimeout!); // TODO: maybe it can be undefined\n    if (this.props.delayPressOut) {\n      this.pressOutTimeout = setTimeout(() => {\n        if (this.STATE === TOUCHABLE_STATE.UNDETERMINED) {\n          this.moveToState(TOUCHABLE_STATE.BEGAN);\n        }\n        this.moveToState(TOUCHABLE_STATE.UNDETERMINED);\n        this.pressOutTimeout = null;\n      }, this.props.delayPressOut);\n    } else {\n      if (this.STATE === TOUCHABLE_STATE.UNDETERMINED) {\n        this.moveToState(TOUCHABLE_STATE.BEGAN);\n      }\n      this.moveToState(TOUCHABLE_STATE.UNDETERMINED);\n    }\n  }\n\n  componentDidMount() {\n    this.reset();\n  }\n  // Reset timeout to prevent memory leaks.\n  reset() {\n    this.longPressDetected = false;\n    this.pointerInside = true;\n    clearTimeout(this.pressInTimeout!);\n    clearTimeout(this.pressOutTimeout!);\n    clearTimeout(this.longPressTimeout!);\n    this.pressOutTimeout = null;\n    this.longPressTimeout = null;\n    this.pressInTimeout = null;\n  }\n\n  // All states' transitions are defined here.\n  moveToState(newState: TouchableState) {\n    if (newState === this.STATE) {\n      // Ignore dummy transitions\n      return;\n    }\n    if (newState === TOUCHABLE_STATE.BEGAN) {\n      // First touch and moving inside\n      this.props.onPressIn?.();\n    } else if (newState === TOUCHABLE_STATE.MOVED_OUTSIDE) {\n      // Moving outside\n      this.props.onPressOut?.();\n    } else if (newState === TOUCHABLE_STATE.UNDETERMINED) {\n      // Need to reset each time on transition to UNDETERMINED\n      this.reset();\n      if (this.STATE === TOUCHABLE_STATE.BEGAN) {\n        // ... and if it happens inside button.\n        this.props.onPressOut?.();\n      }\n    }\n    // Finally call lister (used by subclasses)\n    this.props.onStateChange?.(this.STATE, newState);\n    // ... and make transition.\n    this.STATE = newState;\n  }\n\n  onGestureEvent = ({\n    nativeEvent: { pointerInside },\n  }: GestureEvent<NativeViewGestureHandlerPayload>) => {\n    if (this.pointerInside !== pointerInside) {\n      if (pointerInside) {\n        this.onMoveIn();\n      } else {\n        this.onMoveOut();\n      }\n    }\n    this.pointerInside = pointerInside;\n  };\n\n  onHandlerStateChange = ({\n    nativeEvent,\n  }: HandlerStateChangeEvent<NativeViewGestureHandlerPayload>) => {\n    const { state } = nativeEvent;\n    if (state === State.CANCELLED || state === State.FAILED) {\n      // Need to handle case with external cancellation (e.g. by ScrollView)\n      this.moveToState(TOUCHABLE_STATE.UNDETERMINED);\n    } else if (\n      // This platform check is an implication of slightly different behavior of handlers on different platform.\n      // And Android \"Active\" state is achieving on first move of a finger, not on press in.\n      // On iOS event on \"Began\" is not delivered.\n      state === (Platform.OS !== 'android' ? State.ACTIVE : State.BEGAN) &&\n      this.STATE === TOUCHABLE_STATE.UNDETERMINED\n    ) {\n      // Moving inside requires\n      this.handlePressIn();\n    } else if (state === State.END) {\n      const shouldCallOnPress =\n        !this.longPressDetected &&\n        this.STATE !== TOUCHABLE_STATE.MOVED_OUTSIDE &&\n        this.pressOutTimeout === null;\n      this.handleGoToUndetermined();\n      if (shouldCallOnPress) {\n        // Calls only inside component whether no long press was called previously\n        this.props.onPress?.();\n      }\n    }\n  };\n\n  onLongPressDetected = () => {\n    this.longPressDetected = true;\n    // Checked for in the caller of `onLongPressDetected`, but better to check twice\n    this.props.onLongPress?.();\n  };\n\n  componentWillUnmount() {\n    // To prevent memory leaks\n    this.reset();\n  }\n\n  onMoveIn() {\n    if (this.STATE === TOUCHABLE_STATE.MOVED_OUTSIDE) {\n      // This call is not throttled with delays (like in RN's implementation).\n      this.moveToState(TOUCHABLE_STATE.BEGAN);\n    }\n  }\n\n  onMoveOut() {\n    // Long press should no longer be detected\n    clearTimeout(this.longPressTimeout!);\n    this.longPressTimeout = null;\n    if (this.STATE === TOUCHABLE_STATE.BEGAN) {\n      this.handleMoveOutside();\n    }\n  }\n\n  render() {\n    const hitSlop =\n      (typeof this.props.hitSlop === 'number'\n        ? {\n            top: this.props.hitSlop,\n            left: this.props.hitSlop,\n            bottom: this.props.hitSlop,\n            right: this.props.hitSlop,\n          }\n        : this.props.hitSlop) ?? undefined;\n\n    const coreProps = {\n      accessible: this.props.accessible !== false,\n      accessibilityLabel: this.props.accessibilityLabel,\n      accessibilityHint: this.props.accessibilityHint,\n      accessibilityRole: this.props.accessibilityRole,\n      // TODO: check if changed to no 's' correctly, also removed 2 props that are no longer available: `accessibilityComponentType` and `accessibilityTraits`,\n      // would be good to check if it is ok for sure, see: https://github.com/facebook/react-native/issues/24016\n      accessibilityState: this.props.accessibilityState,\n      accessibilityActions: this.props.accessibilityActions,\n      onAccessibilityAction: this.props.onAccessibilityAction,\n      nativeID: this.props.nativeID,\n      onLayout: this.props.onLayout,\n    };\n\n    return (\n      <BaseButton\n        style={this.props.containerStyle}\n        onHandlerStateChange={\n          // TODO: not sure if it can be undefined instead of null\n          this.props.disabled ? undefined : this.onHandlerStateChange\n        }\n        onGestureEvent={this.onGestureEvent}\n        hitSlop={hitSlop}\n        userSelect={this.props.userSelect}\n        shouldActivateOnStart={this.props.shouldActivateOnStart}\n        disallowInterruption={this.props.disallowInterruption}\n        testID={this.props.testID}\n        touchSoundDisabled={this.props.touchSoundDisabled ?? false}\n        enabled={!this.props.disabled}\n        {...this.props.extraButtonProps}>\n        <Animated.View {...coreProps} style={this.props.style}>\n          {this.props.children}\n        </Animated.View>\n      </BaseButton>\n    );\n  }\n}\n"]}