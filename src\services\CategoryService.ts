// Category Service for Database Operations

import { v4 as uuidv4 } from 'react-native-uuid';
import databaseService from '../database';
import { Category } from '../types';

export class CategoryService {
  async getAllCategories(): Promise<Category[]> {
    const query = `
      SELECT * FROM categories 
      ORDER BY is_default DESC, name ASC
    `;
    
    const rows = await databaseService.executeQuery(query);
    return rows.map(this.mapRowToCategory);
  }

  async getCategoriesByType(type: 'income' | 'expense'): Promise<Category[]> {
    const query = `
      SELECT * FROM categories 
      WHERE type = ?
      ORDER BY is_default DESC, name ASC
    `;
    
    const rows = await databaseService.executeQuery(query, [type]);
    return rows.map(this.mapRowToCategory);
  }

  async getCategoryById(id: string): Promise<Category | null> {
    const query = 'SELECT * FROM categories WHERE id = ?';
    const rows = await databaseService.executeQuery(query, [id]);
    return rows.length > 0 ? this.mapRowToCategory(rows[0]) : null;
  }

  async createCategory(category: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<Category> {
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const query = `
      INSERT INTO categories (
        id, name, type, color, icon, is_default, parent_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await databaseService.executeQuery(query, [
      id,
      category.name,
      category.type,
      category.color,
      category.icon,
      category.isDefault ? 1 : 0,
      category.parentId || null,
      now,
      now,
    ]);

    const createdCategory = await this.getCategoryById(id);
    if (!createdCategory) {
      throw new Error('Failed to create category');
    }

    return createdCategory;
  }

  async updateCategory(id: string, updates: Partial<Category>): Promise<Category> {
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (updates.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(updates.name);
    }
    if (updates.type !== undefined) {
      updateFields.push('type = ?');
      updateValues.push(updates.type);
    }
    if (updates.color !== undefined) {
      updateFields.push('color = ?');
      updateValues.push(updates.color);
    }
    if (updates.icon !== undefined) {
      updateFields.push('icon = ?');
      updateValues.push(updates.icon);
    }
    if (updates.parentId !== undefined) {
      updateFields.push('parent_id = ?');
      updateValues.push(updates.parentId);
    }

    updateFields.push('updated_at = ?');
    updateValues.push(new Date().toISOString());
    updateValues.push(id);

    const query = `UPDATE categories SET ${updateFields.join(', ')} WHERE id = ?`;
    await databaseService.executeQuery(query, updateValues);

    const updatedCategory = await this.getCategoryById(id);
    if (!updatedCategory) {
      throw new Error('Failed to update category');
    }

    return updatedCategory;
  }

  async deleteCategory(id: string): Promise<void> {
    // Check if category is default
    const category = await this.getCategoryById(id);
    if (category?.isDefault) {
      throw new Error('Cannot delete default category');
    }

    // Check if category has transactions
    const transactionQuery = 'SELECT COUNT(*) as count FROM transactions WHERE category_id = ?';
    const transactionRows = await databaseService.executeQuery(transactionQuery, [id]);
    
    if (transactionRows[0].count > 0) {
      throw new Error('Cannot delete category with existing transactions');
    }

    // Check if category has subcategories
    const subcategoryQuery = 'SELECT COUNT(*) as count FROM categories WHERE parent_id = ?';
    const subcategoryRows = await databaseService.executeQuery(subcategoryQuery, [id]);
    
    if (subcategoryRows[0].count > 0) {
      throw new Error('Cannot delete category with subcategories');
    }

    const query = 'DELETE FROM categories WHERE id = ?';
    await databaseService.executeQuery(query, [id]);
  }

  async getSubcategories(parentId: string): Promise<Category[]> {
    const query = `
      SELECT * FROM categories 
      WHERE parent_id = ?
      ORDER BY name ASC
    `;
    
    const rows = await databaseService.executeQuery(query, [parentId]);
    return rows.map(this.mapRowToCategory);
  }

  async getParentCategories(type?: 'income' | 'expense'): Promise<Category[]> {
    let query = `
      SELECT * FROM categories 
      WHERE parent_id IS NULL
    `;
    const params: any[] = [];

    if (type) {
      query += ' AND type = ?';
      params.push(type);
    }

    query += ' ORDER BY is_default DESC, name ASC';
    
    const rows = await databaseService.executeQuery(query, params);
    return rows.map(this.mapRowToCategory);
  }

  async getCategoryUsageStats(): Promise<{ categoryId: string; categoryName: string; transactionCount: number; totalAmount: number }[]> {
    const query = `
      SELECT 
        c.id as categoryId,
        c.name as categoryName,
        COUNT(t.id) as transactionCount,
        COALESCE(SUM(t.amount), 0) as totalAmount
      FROM categories c
      LEFT JOIN transactions t ON c.id = t.category_id
      GROUP BY c.id, c.name
      HAVING transactionCount > 0
      ORDER BY totalAmount DESC
    `;

    return await databaseService.executeQuery(query);
  }

  private mapRowToCategory(row: any): Category {
    return {
      id: row.id,
      name: row.name,
      type: row.type,
      color: row.color,
      icon: row.icon,
      isDefault: Boolean(row.is_default),
      parentId: row.parent_id,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}

export const categoryService = new CategoryService();
